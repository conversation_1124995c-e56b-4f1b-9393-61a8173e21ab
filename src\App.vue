<template>
  <div id="app">
    <router-view v-if="isRouterAlive"/>
  </div>
</template>

<script>
import { getCaptainListFromPc } from './api/signUp';
export default {
  name: 'app',
  provide() {
    return {
      reload: this.reload
    };
  },
  data() {
    return {
      isRouterAlive: true
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    }
  },
  // 随便发起一个请求触发拦截
  beforeCreate() {
    getCaptainListFromPc();
  }
};
</script>

<style lang="scss">
@import "~@/assets/css/variables";

// 确保全局样式正确应用
#app {
  width: 100%;
  min-height: 100vh;
  font-family: $font-family-primary;
  color: $color-text-primary;
  background-color: $color-bg-primary;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 确保响应式图片和媒体元素正确显示
img, video, iframe {
  max-width: 100%;
  height: auto;
}

// 确保表格在移动端可以滚动
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

// 全局响应式字体大小调整 - 只影响PC端页面
html {
  font-size: 14px;
}

// 只对PC端页面进行字体调整，避免影响移动端组件
#app:not(#mobile) {
  @media screen and (min-width: 768px) {
    font-size: 15px;
  }

  @media screen and (min-width: 1024px) {
    font-size: 16px;
  }
}

// 确保移动端视口设置正确
@viewport {
  width: device-width;
  zoom: 1.0;
}

// 移动端触摸优化
* {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

// 滚动条优化
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

// 确保Element UI组件响应式
.el-row {
  @media screen and (max-width: 767px) {
    .el-col {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Vant组件响应式优化
.van-nav-bar {
  @media screen and (min-width: 768px) {
    display: none;
  }
}

// 确保弹窗在移动端正确显示
.el-dialog {
  @media screen and (max-width: 767px) {
    width: 95% !important;
    margin: 0 auto !important;
    top: 5% !important;
  }
}

.van-popup {
  @media screen and (min-width: 768px) {
    max-width: 500px;
    margin: 0 auto;
  }
}
</style>
