const map = {
  1: '报名成功',
  2: '审核中',
  3: '等待组队',
  4: '报名失败',
  5: '候补中',
  6: '活动人数已满'
};
export const addFields = (res) => {
  res.data.data.map((item) => {
    item.teamLeaderId = item.id;
    item.teamLeaderName = item.name;
    return item;
  });
  return res;
};
export const translate = (res) => {
  if (res.status !== 200) { return []; }
  res.data.data.map(item => {
    // 添加是否必填参数
    item.must = item.isRequired === 1;
    return item;
  });
  return res;
};
export const addStatus = (res) => {
  res.data.data.records.map(item => {
    item.statusText = map[item.status];
    return item;
  });
  return res;
};
export const sortData = (res) => {
  res.data.data.records.sort((a, b) => a.id - b.id);
  return res;
};
export const sortCapsData = (res) => {
  res.data.data.sort((a, b) => a.id - b.id);
  return res;
};
