<!--
 * @Author: your name
 * @Date: 2020-11-13 10:32:05
 * @LastEditTime: 2020-12-04 11:16:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /page/src/views/pc/detail/userInfoForm.vue
-->
<template>
  <div style="marginLeft:40px">
    <el-form ref="userInfo" label-position="right" >
      <div v-for="(item, index) of signUpFormList" :key="index">
        <el-form-item :label="item.fieldName">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input
                v-if="item.fieldName !== '性别' && item.fieldName !=='是否携带亲属'"
                v-model="userInfoField[item.fieldName]"
                :disabled="item.fieldName === '姓名' || item.fieldName === '电话'"
              />
              <el-input v-else-if="item.fieldName==='性别'" disabled :value="userInfoField[item.fieldName] === 1 ? '男': '女'" />
              <el-input v-else-if="item.fieldName==='是否携带亲属'" disabled :value="userInfoField[item.fieldName] === 1 ? '是': '否'" />
            </el-col>
          </el-row>
        </el-form-item>
      </div>
      <el-form-item>
        <el-row>
          <el-col>
            <el-button style="float:right" :loading='loading' type="primary" @click="modify">修改</el-button>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// 直接获取字段
export default {
  props: {
    userInfoField: {
      type: Object,
      require: true
    },
    signUpFormList: {
      type: Array,
      require: true
    },
    loading: {
      type: Boolean,
      require: false
    }
  },
  methods: {
    getLabel(key) {
      let label;
      // 通过抛出错误强制停止循环
      try {
        this.signUpFormList.forEach((item, index) => {
          if (item.key === key) {
            label = item.fileName;
            throw new Error('stopIteration');
          }
        });
      } catch (e) {}
      return label;
    },
    modify() {
      this.$emit('emit-modify');
    }
  }
};
</script>

<style>
</style>
