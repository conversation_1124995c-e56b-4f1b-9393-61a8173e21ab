// ===== 设计系统变量 =====

// === 断点系统 ===
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);

// === 美化颜色系统 ===
// 主色调 - 现代蓝色系
$color-primary: #2563eb;        // 专业蓝色
$color-primary-light: #3b82f6;  // 亮蓝色
$color-primary-dark: #1d4ed8;   // 深蓝色

// 次色调 - 优雅紫色系
$color-secondary: #7c3aed;      // 优雅紫色
$color-secondary-light: #8b5cf6; // 亮紫色
$color-secondary-dark: #6d28d9;  // 深紫色

// 辅助色 - 清新青色系
$color-tertiary: #06b6d4;       // 现代青色
$color-tertiary-light: #22d3ee; // 亮青色
$color-tertiary-dark: #0891b2;  // 深青色

// === 美化功能色 ===
$color-success: #10b981;        // 现代绿色
$color-success-light: #34d399;  // 亮绿色
$color-success-dark: #059669;   // 深绿色

$color-warning: #f59e0b;        // 现代橙色
$color-warning-light: #fbbf24;  // 亮橙色
$color-warning-dark: #d97706;   // 深橙色

$color-error: #ef4444;          // 现代红色
$color-error-light: #f87171;    // 亮红色
$color-error-dark: #dc2626;     // 深红色

$color-info: #3b82f6;           // 信息蓝色
$color-info-light: #60a5fa;     // 亮信息蓝
$color-info-dark: #2563eb;      // 深信息蓝

// === 现代中性色 ===
// 文字颜色
$color-text-primary: #1f2937;     // 深灰色 - 主要文字
$color-text-secondary: #4b5563;   // 中灰色 - 次要文字
$color-text-tertiary: #6b7280;    // 浅灰色 - 辅助文字
$color-text-placeholder: #9ca3af; // 占位符灰色
$color-text-disabled: #d1d5db;    // 禁用状态灰色
$color-text-inverse: #ffffff;     // 反色文字

// 背景颜色
$color-bg-primary: #ffffff;       // 纯白背景
$color-bg-secondary: #f9fafb;     // 浅灰背景
$color-bg-tertiary: #f3f4f6;      // 基础背景
$color-bg-disabled: #e5e7eb;      // 禁用背景

// 边框颜色
$color-border-primary: #e5e7eb;   // 基础边框
$color-border-secondary: #f3f4f6; // 浅边框
$color-border-dark: #d1d5db;      // 深边框

// === 字体系统 ===
$font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-code: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

// 行高
$line-height-tight: 1.2;
$line-height-base: 1.5;
$line-height-loose: 1.8;

// === 间距系统 ===
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;
$spacing-xxxl: 64px;

// === 圆角系统 ===
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// === 阴影系统 ===
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
$shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
$shadow-xl: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);

// === Z-index 系统 ===
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// === 兼容性变量 (保持向后兼容) ===
$promary-color: $color-secondary; // 保持原有变量名

// === 响应式函数和混合器 ===
@function px2rem($px) {
  $rem: 37.5px;
  @return ($px / $rem) * 1rem;
}

// 响应式断点混合器
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    $value: map-get($breakpoints, $breakpoint);
    @if $value == 0 {
      @content;
    } @else {
      @media (min-width: $value) {
        @content;
      }
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

// 文本省略混合器
@mixin multiline-omit($line) {
  width: 100%;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $line;
  word-break: break-all;
}

// 单行文本省略
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// Flexbox 居中
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 清除浮动
@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 按钮重置
@mixin button-reset {
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  outline: none;
}

// 卡片样式
@mixin card {
  background: $color-bg-primary;
  border-radius: $border-radius-base;
  box-shadow: $shadow-base;
  padding: $spacing-base;
}

// 输入框样式
@mixin input-base {
  border: 1px solid $color-border-primary;
  border-radius: $border-radius-base;
  padding: $spacing-sm $spacing-base;
  font-size: $font-size-base;
  line-height: $line-height-base;
  transition: border-color 0.3s ease;

  &:focus {
    border-color: $color-primary;
    outline: none;
    box-shadow: 0 0 0 2px rgba($color-primary, 0.2);
  }
}
