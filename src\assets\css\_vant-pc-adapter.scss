// ===== Vant 组件 PC 端适配样式 =====
// 最小化修复方案：只解决标签页文本截断问题，避免全局字体影响

// === 修复活动列表页面标签页文本截断问题 ===
.activity-list-container {
  // 修复标签页文本被截断的问题
  .van-tab__text--ellipsis {
    @media screen and (min-width: 768px) {
      overflow: visible !important;
      text-overflow: unset !important;
      white-space: nowrap !important;
      max-width: none !important;
    }
  }

  // 确保标签页在PC端有足够的高度
  @media screen and (min-width: 768px) {
    .modern-tabs.van-tabs {
      .van-tabs__wrap {
        min-height: 50px;

        .van-tabs__nav {
          min-height: 44px;

          .van-tab {
            min-height: 44px;
            padding: 10px 16px;

            // 标签页文本
            .van-tab__text {
              line-height: 1.4;
              white-space: nowrap;
              overflow: visible;
              text-overflow: unset;
            }

            // 简单的悬浮效果
            &:hover {
              background-color: rgba(0, 0, 0, 0.05);
              transition: background-color 0.3s ease;
            }
          }
        }
      }
    }
  }

  // 大屏幕适配
  @media screen and (min-width: 1200px) {
    .modern-tabs.van-tabs {
      .van-tabs__wrap {
        min-height: 56px;

        .van-tabs__nav {
          min-height: 50px;

          .van-tab {
            min-height: 50px;
            padding: 12px 20px;
          }
        }
      }
    }
  }
}