<template>
  <div id="mobile">
    <router-view v-if="isRouterAlive"/>
  </div>
</template>

<script>
import { getCaptainListFromMobile } from './api/signUp';
export default {
  name: 'mobile',
  provide() {
    return {
      reload: this.reload
    };
  },
  data() {
    return {
      isRouterAlive: true
    };
  },
  methods: {
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      });
    }
  },
  // 随便发起一个请求触发拦截
  beforeCreate() {
    getCaptainListFromMobile();
  }
};
</script>
<style lang="scss">

</style>
