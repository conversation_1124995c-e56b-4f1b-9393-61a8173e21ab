html {
  overflow-y: scroll;
}

:root {
  overflow-y: auto;
  overflow-x: hidden;
}

:root body {
  position: absolute;
}

body {
  width: 100vw;
  overflow: hidden;
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", <PERSON><PERSON>, sans-serif;
  ;
}

p {
  margin: 0;
  font-family: SourceHanSansCN-Light;
}



a {
  text-decoration: none;
  cursor: pointer;
}

img {
  vertical-align: middle;
}

ul {
  list-style: none;
}

::-webkit-scrollbar {
  /*滚动条整体样式  宽高要求一致，容易对文件*/
  width: 0px;
  /*高宽分别对应横竖滚动条的尺寸*/
  height: 3px;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 2px;
  background: #005983;
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  border-radius: 2px;
  background: rgba($color: $color-primary, $alpha: 0.3);
}

::-webkit-scrollbar-button {
  width: 5px;
  height: 3px;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
  transition: all .5s;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}


/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all .5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all .5s;
}

.breadcrumb-leave-active {
  position: absolute;
}



/*覆写左侧栏菜单样式*/
// .el-menu {
//   border-right: none;
//   .el-menu-item {
//     &.is-active {
//       font-weight: 700;
//       background-color: #82dbe4 !important;
//       transform: translateX(20px);
//       border-radius: 20px;
//       transition: all .3s ease-in;
//     }
//   }

//   .el-submenu__title i,
//   .el-menu-item i {
//     width: 30px;
//     margin-right: 4px;
//     text-align: center;
//     font-size: 18px;
//     color: #fff;
//   }
// }

.el-select {
  width: 100%;
}

.van-tab--active {
  color: $promary-color;
}
.van-tabs__line {
  background-color: $promary-color;
  width: px2rem(60px);
}

.van-nav-bar {
  background: $promary-color;
  .van-icon,.van-nav-bar__text {
    color: #fff;
    opacity: 1;
  }
  .van-nav-bar__title{
    color: #fff;      
  }
}