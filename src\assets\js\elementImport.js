import {
  Table,
  TableColumn,
  Button,
  ButtonGroup,
  Row,
  Col,
  Popover,
  Timeline,
  TimelineItem,
  Dialog,
  Menu,
  Submenu,
  MenuItemGroup,
  MenuItem,
  Breadcrumb,
  BreadcrumbItem,
  Select,
  Option,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Pagination,
  Input,
  Form,
  FormItem,
  Checkbox,
  CheckboxGroup,
  DatePicker,
  TimePicker,
  Message,
  MessageBox,
  Radio,
  RadioGroup,
  Loading,
  Upload,
  Avatar,
  Transfer,
  Tabs,
  TabPane,
  Image,
  Tree,
  Progress,
  Divider,
  Container,
  Header,
  Main,
  Cascader,
  Card
} from 'element-ui';
import Vue from 'vue';

const components = {
  Table,
  TableColumn,
  Button,
  ButtonGroup,
  Row,
  Col,
  Popover,
  Timeline,
  TimelineItem,
  Dialog,
  Menu,
  Submenu,
  MenuItemGroup,
  MenuItem,
  Breadcrumb,
  BreadcrumbItem,
  Select,
  Option,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Pagination,
  Input,
  Form,
  FormItem,
  Checkbox,
  CheckboxGroup,
  DatePicker,
  TimePicker,
  Radio,
  RadioGroup,
  Upload,
  Avatar,
  Transfer,
  Tabs,
  TabPane,
  Image,
  Tree,
  Progress,
  Divider,
  Container,
  Header,
  <PERSON>,
  <PERSON>r,
  <PERSON>
};

for (const key in components) {
  Vue.use(components[key]);
}
Vue.use(Loading.directive);
Vue.prototype.$loading = Loading.service;
Vue.prototype.$message = Message;
Vue.prototype.$messageBox = MessageBox;
Vue.prototype.$confirm = MessageBox.confirm;
