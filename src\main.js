import Vue from 'vue';
import App from './App.vue';
import router from './router/pc';
import store from './store';
import '@/assets/js/elementImport';
import '@/element-ui-theme';
import '@/assets/css/index.scss';
import 'font-awesome/css/font-awesome.min.css';
import 'element-ui/lib/theme-chalk/display.css';
import 'element-ui/lib/theme-chalk/index.css';
import DataTable from './components/DataTable.vue';

Vue.component('data-table', DataTable);
Vue.config.productionTip = false;

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app');
