# REQUEST_URL 用于后台地址设置
# "8765:8080" 8765可更改，8080不可更改

version: "3"
services:
  web:
    # 指定运行的镜像
    image: ws/wechatservicepage

    # 容器名称
    container_name: wechatservicepage

    #容器挂了的重启策略
    restart: always

    #端口映射
    ports:
      - "9090:8080"

    #从文件添加环境变量   
    env_file:
      - ./env

    #在容器启动前做的初始化配置(例如环境变量的替换)
    entrypoint: /home/<USER>

    #将主机的数据卷/文件挂在到容器里
    #主机数据卷:容器目录
    volumes:
      - ./common/logs/proxy:/var/log/nginx

    ulimits:
      nproc: 100000
      nofile:
        soft: 100000
        hard: 200000
