export const addField = res => {
  res.data.data.map((item, index) => {
    item.index = index + 1;
    item.text = item.teamName;
    return item;
  });
  return res;
};
export const translate = res => {
  res.data.data.map((item, index) => {
    item.index = index + 1;
    const info = JSON.parse(item.applyInfo);
    item.name = info['姓名'];
    return item;
  });
  return res;
};
export const sortData = res => {
  res.data.data.sort((a, b) => a.id - b.id);
  return res;
};
