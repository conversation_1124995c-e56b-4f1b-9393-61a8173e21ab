const path = require('path');

module.exports = {
  pages: {
    admin: {
      entry: './src/main.js',
      template: './public/admin.html',
      title: '活动发布'
    },
    index: {
      entry: './src/mobile.js',
      template: './public/index.html',
      title: '活动报名'
    }
  },
  css: {
    loaderOptions: {
      sass: {
        // 配置sass-resources定义全局css变量
        prependData: '@import "@/assets/css/_variables.scss";'
      },
      less: {
        // 若使用 less-loader@5，请移除 lessOptions 这一级，直接配置选项。
        lessOptions: {
          modifyVars: {
            // 直接覆盖变量
            // 或者可以通过 less 文件覆盖（文件路径为绝对路径）
            hack: '@import "@/assets/css/vant_theme.less";'
          }
        }
      }
    }
  },
  lintOnSave: true,
  configureWebpack: {
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src')
      }
    },
    devtool: 'source-map'
  },
  devServer: {
    port: 8080,
    proxy: {
      '/api': {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '/back': {
        target: process.env.VUE_APP_BASE_LOGIN,
        changeOrigin: true,
        pathRewrite: {
          '^/back': ''
        }
      },
      '/image': {
        target: process.env.VUE_APP_BASE_IMAGE,
        changeOrigin: true,
        pathRewrite: {
          // '^/image': ''
        }
      }
    },
    disableHostCheck: true
  }
};
