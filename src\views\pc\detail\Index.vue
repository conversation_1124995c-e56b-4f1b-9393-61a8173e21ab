<template>
  <div class="activity-detail-container">
    <!-- 返回按钮 -->
    <div class="back-button-wrapper">
      <el-button
        type="text"
        icon="el-icon-arrow-left"
        class="back-button"
        @click="back"
      >
        返回活动列表
      </el-button>
    </div>

    <!-- 活动信息卡片 -->
    <div class="activity-info-card">
      <ActivityInfo
        :activity="activityDetail"
        @editActivity="editActivity"
        @activityControlFromPc="activityControlFromPc"
        @drawFromPc="drawFromPc"
      />
    </div>

    <!-- 报名功能卡片 -->
    <div class="signup-card" v-if="activityDetail.status === 2">
      <div class="card-header">
        <div class="header-content">
          <i class="el-icon-user header-icon"></i>
          <h3 class="card-title">活动报名</h3>
        </div>
        <div class="signup-status">
          <div class="status-item">
            <span class="status-label">我的报名状态</span>
            <span class="status-value" :class="getMySignUpStatusClass()">{{ getMySignUpStatusText() }}</span>
          </div>
        </div>
      </div>

      <div class="card-content">
        <div class="signup-actions">
          <el-button
            v-if="!mySignUpInfo || mySignUpInfo.status === 4"
            type="primary"
            size="large"
            icon="el-icon-user"
            @click="openSignUpDialog"
            :disabled="!canSignUp"
          >
            {{ mySignUpInfo && mySignUpInfo.status === 4 ? '重新报名' : '立即报名' }}
          </el-button>

          <el-button
            v-if="mySignUpInfo && mySignUpInfo.status !== 4"
            type="info"
            size="large"
            icon="el-icon-view"
            @click="viewMySignUp"
          >
            查看我的报名信息
          </el-button>

          <el-button
            v-if="mySignUpInfo && [1, 2, 5].includes(mySignUpInfo.status)"
            type="danger"
            size="large"
            icon="el-icon-delete"
            @click="cancelSignUp"
          >
            取消报名
          </el-button>
        </div>

        <div class="signup-info" v-if="activityDetail.activityDetail">
          <div class="info-item">
            <span class="info-label">报名截止时间：</span>
            <span class="info-value">{{ activityDetail.activityDetail.enlistEndTime }}</span>
          </div>
          <div class="info-item" v-if="activityDetail.activityDetail.enlistCost > 0">
            <span class="info-label">报名费用：</span>
            <span class="info-value">¥{{ activityDetail.activityDetail.enlistCost }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">活动人数限制：</span>
            <span class="info-value">{{ activityDetail.participationNum }}/{{ activityDetail.actNum }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 报名数据统计卡片 -->
    <div class="statistics-card">
      <div class="card-header">
        <div class="header-content">
          <i class="el-icon-data-analysis header-icon"></i>
          <h3 class="card-title">报名数据统计</h3>
        </div>
        <div class="statistics-summary">
          <div class="summary-item">
            <span class="summary-label">总报名人数</span>
            <span class="summary-value">{{ signUpTotal }}</span>
          </div>
        </div>
      </div>

      <div class="card-content">
        <data-table
          :flag="flag"
          class="modern-table"
          :tableTitle="statisticTitle"
          :buttonList="buttonList"
          isShowSelector
          @check="check"
          @pass="pass"
          @fail="fail"
          @deleteSignUp="deleteSignUp"
          :tableData="signUpData"
          :total="signUpTotal"
          :pageSize="signUpPageSize"
          :pageIndex="signUpPageIndex"
          @page-size-change="changeSignUpPageSize"
          @page-index-change="changeSignUpPageIndex"
          @selectedTitleChange="handleSelectedTitleChange"
        >
          <template #filter>
            <div class="filter-tabs-wrapper" v-if="isExamine || isAllowedAlternate">
              <el-tabs v-model="activeFilterTab" @tab-click="handleFilterSignUpList" class="modern-tabs">
                <el-tab-pane
                  v-for="(item, index) in filterSignUpTabList"
                  :key="index"
                  :label="item.label"
                  :name="String(item.value)"
                />
              </el-tabs>
            </div>
          </template>
        </data-table>
      </div>
    </div>

    <!-- <Title :titleConfig="priceInfo" :top="70" />
    <el-divider />
    <data-table
      :flag="!flag"
      class="price-table"
      :tableTitle="priceTitle"
      :tableData="lotteryData"
      :total="lotteryTotal"
      :pageSize="lotteryPageSize"
      :pageIndex="lotteryPageIndex"
      @page-size-change="changeLotteryPageSize"
      @page-index-change="changeLotteryPageIndex"
    /> -->
    <!-- 报名表单对话框 -->
    <el-dialog
      title="活动报名"
      :visible.sync="signUpDialogVisible"
      :close-on-click-modal="false"
      width="600px"
    >
      <el-form
        :model="signUpFormData"
        label-width="120px"
        v-loading="signUpLoading"
      >
        <el-form-item
          v-for="field in signUpFormFields"
          :key="field.fieldName"
          :label="field.fieldName"
          :required="field.must"
        >
          <!-- 性别选择 -->
          <el-radio-group
            v-if="field.fieldName === '性别'"
            v-model="signUpFormData[field.fieldName]"
          >
            <el-radio label="1">男</el-radio>
            <el-radio label="2">女</el-radio>
          </el-radio-group>

          <!-- 是否携带亲属 -->
          <el-radio-group
            v-else-if="field.fieldName === '是否携带亲属'"
            v-model="signUpFormData[field.fieldName]"
          >
            <el-radio label="1">是</el-radio>
            <el-radio label="2">否</el-radio>
          </el-radio-group>

          <!-- 队伍选择 -->
          <el-select
            v-else-if="field.fieldName === '队伍'"
            v-model="signUpFormData[field.fieldName]"
            placeholder="请选择队伍"
            style="width: 100%"
          >
            <el-option
              v-for="team in teamList"
              :key="team.id"
              :label="team.teamName"
              :value="team.teamName"
              :disabled="team.disabled"
            />
          </el-select>

          <!-- 普通输入框 -->
          <el-input
            v-else
            v-model="signUpFormData[field.fieldName]"
            :type="field.fieldName === '电话' ? 'tel' : 'text'"
            :placeholder="`请输入${field.fieldName}`"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="signUpDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitSignUp" :loading="signUpLoading">
          提交报名
        </el-button>
      </div>
    </el-dialog>

    <!-- 查看报名信息对话框 -->
    <el-dialog
      title="我的报名信息"
      :visible.sync="viewSignUpDialogVisible"
      width="500px"
    >
      <div class="signup-info-view">
        <div
          v-for="(value, key) in signUpFormData"
          :key="key"
          class="info-row"
        >
          <span class="info-label">{{ key }}：</span>
          <span class="info-value">{{ value }}</span>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="viewSignUpDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog
      v-if="dialogFormVisible"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      width="600px"
    >
      <div slot="title">
        <Title :titleConfig="dialogInfo" :top="0" />
      </div>
      <UserInfoForm
      @emit-modify="modifyData"
      :userInfoField="selectedUser"
      :loading="modifyLoading"
      :signUpFormList="signUpFormList"/>
    </el-dialog>
  </div>
</template>

<script>
import ActivityInfo from './activityInfo';
import Title from '@/components/title';
import UserInfoForm from './userInfoForm';
import requestMixin from '../../../mixin/requestMixin';
import { getActivityDetailFromPc, deleteActivityFromPc } from '../../../api/activity/index';
import { getSignUpListFromPc, getSignUpFieldFromPc, deleteSignUpInfoFromPc, auditSignUpFromPc, adminUpdateSignUpFromPc, getMySignUpFromMobile, signUpFromMobile, deleteSignUpInfoFromMobile } from '../../../api/signUp/index';
import { getLotteryListFromPc, drawFromPc } from '../../../api/price/index';
import { getTeamListFromMobile } from '../../../api/team/index';

const STATUS_MAP = {
  已审核: '1,4',
  未审核: 2,
  候补: 5
};

export default {
  components: {
    ActivityInfo,
    Title,
    UserInfoForm
  },
  mixins: [requestMixin],
  inject: ['reload'],
  data() {
    return {
      flag: true,
      activityDetail: {},
      signUpFormList: [],
      // 报名数据统计中表格数据
      signUpData: [],
      signUpTotal: 0,
      signUpPageIndex: 1,
      signUpPageSize: 10,
      filterParams: {},

      // 中奖名单数据
      lotteryData: [],
      lotteryTotal: 0,
      lotteryPageIndex: 1,
      lotteryPageSize: 10,

      selectedUser: {},
      statisticInfo: {
        fontSize: 25,
        title: '报名数据统计'
      },
      // priceInfo: {
      //   fontSize: 25,
      //   title: '中奖名单统计'
      // },
      dialogInfo: {
        fontSize: 20,
        title: '报名人具体信息'
      },
      dialogFormVisible: false,
      statisticTitle: [
        { label: '序号', prop: 'aid' },
        { label: '姓名', prop: 'name' },
        { label: '部门', prop: 'department' },
        { label: '状态', prop: 'statusText' }
      ],
      buttonList: [
        {
          text: '查看',
          limit: 1,
          only: 1,
          event: 'check'
        }
      ],
      priceTitle: [
        { label: '序号', prop: 'sortId' },
        { label: '姓名', prop: 'name' },
        { label: '奖项', prop: 'rewardLevel' },
        { label: '奖品', prop: 'rewardName' }
      ],
      priceData: [],
      showReason: false,
      orginalData: {},
      activityStatus: {
        0: '启用',
        1: '删除',
        2: '作废'
      },
      modifyLoading: false,
      // 导出Excel组件数据
      exportData: {
        tableHeader: [{
          checked: false,
          label: '序号',
          prop: 'aid'
        },
        {
          checked: false,
          label: '姓名',
          prop: 'name'
        },
        {
          checked: false,
          label: '电话',
          prop: 'telephone'
        },
        {
          checked: false,
          // 这里设置checked为true是默认勾选状态
          label: '部门',
          prop: 'department'
        },
        {
          checked: false,
          label: '队伍',
          prop: 'team'
        },
        {
          checked: false,
          label: '亲属人数',
          prop: 'relativesNumber'
        },
        {
          checked: false,
          label: '亲属备注',
          prop: 'relativesRemark'
        },
        {
          checked: false,
          label: '上车地点(市区、公司)',
          prop: 'outset'
        },
        {
          checked: false,
          label: '状态',
          prop: 'statusText'
        }],
        signUpData: [],
        excelName: '活动报名数据名单'
      },

      activeFilterTab: 'all',
      filterSignUpTabList: [],
      // 是否允许候补
      isAllowedAlternate: false,
      // 是否需要审核
      isExamine: false,

      // 报名相关数据
      mySignUpInfo: null,
      signUpDialogVisible: false,
      signUpFormFields: [],
      signUpFormData: {},
      signUpLoading: false,
      teamList: [],
      viewSignUpDialogVisible: false
    };
  },
  computed: {
    // 是否可以报名
    canSignUp() {
      if (!this.activityDetail || !this.activityDetail.activityDetail) return false;
      const now = new Date();
      const enlistStartTime = new Date(this.activityDetail.activityDetail.enlistStartTime);
      const enlistEndTime = new Date(this.activityDetail.activityDetail.enlistEndTime);
      return now >= enlistStartTime && now <= enlistEndTime && this.activityDetail.status === 2;
    }
  },
  created() {
    if (!this.$route.query.id) {
      this.$router.push('/Home/list');
    } else {
      this.getActivityDetailFromPc();
      this.getSignUpListFromPc();
      this.getLotteryListFromPc();
      this.getMySignUpInfo();
    }
  },
  methods: {
    // 获取中奖名单
    async getLotteryListFromPc() {
      const [err, res] = await this.request(getLotteryListFromPc, { actId: this.$route.query.id, currentPage: this.lotteryPageIndex, pageSize: this.lotteryPageSize });
      if (err) { return; };
      this.lotteryTotal = res.data.data.total;
      res.data.data.records.map((item, index) => {
        item.sortId = index + (this.lotteryPageIndex - 1) * this.lotteryPageSize + 1;
        return item;
      });
      // 使用nextTick解决在切换pageSize时导致数据未渲染的问题
      this.$nextTick(() => {
        this.lotteryData = res.data.data.records;
      });
    },

    // 获取报名名单
    async getSignUpListFromPc(params) {
      this.signUpData = [];
      const [err, res] = await this.request(
        getSignUpListFromPc,
        {
          actId: this.$route.query.id,
          pageSize: this.signUpPageIndex,
          pageNumber: this.signUpPageSize,
          ...params
        }
      );
      if (err) { return; };
      this.signUpTotal = res.data.data.total;
      res.data.data.records.forEach((item, index) => {
        const info = JSON.parse(item.applyInfo);
        const data = {
          aid: index + (this.signUpPageIndex - 1) * this.signUpPageSize + 1,
          name: info['姓名'],
          department: info['部门'],
          statusText: item.statusText
        };
        Object.assign(item, info, data);
        this.signUpData.push(item);
      });
    },

    handleFilterSignUpList(data) {
      this.signUpPageIndex = 1;
      this.signUpPageSize = 10;
      let params = {};
      if (data.name !== 'all') {
        params = {
          condition: {
            status: STATUS_MAP[data.label]
          }
        };
      }
      this.filterParams = params;
      this.getSignUpListFromPc(params);
    },

    // 获取活动详情
    async getActivityDetailFromPc() {
      const [err, res] = await this.request(getActivityDetailFromPc, { actId: this.$route.query.id });
      if (err) { return; };
      // 判断活动是否需要审核，需要审核的才添加审核按钮
      const { isExamine, isAllowedAlternate } = res.data.data.activityDetail;
      this.isExamine = isExamine === 1;
      this.isAllowedAlternate = isAllowedAlternate === 1;
      this.filterSignUpTabList = [];

      if (this.isExamine) {
        this.buttonList.push(
          {
            text: '审核通过',
            limit: 1,
            event: 'pass'
          },
          {
            text: '审核不通过',
            limit: 1,
            type: 'danger',
            event: 'fail'
          }
        );

        this.filterSignUpTabList = [
          { label: '全部', value: 'all' },
          { label: '已审核', value: 1 },
          { label: '未审核', value: 2 }
        ];
      }

      if (this.isAllowedAlternate) {
        this.filterSignUpTabList.push({ label: '候补', value: 5 });
      }

      this.$nextTick(() => {
        this.activityDetail = res.data.data;
        this.activityDetail.enlistCost = res.data.data.activityDetail !== null ? res.data.data.activityDetail.enlistCost : 0;
      });
    },

    // 报名数据统计标题字段勾选事件
    handleSelectedTitleChange(data) {
      // console.log(data);
      this.activityDetail.fields = data.join(',');
    },

    // 查看报名人具体信息
    check(item) {
      this.selectedUser = JSON.parse(item[0].applyInfo);
      this.selectedUser['状态'] = item[0].status;
      getSignUpFieldFromPc({ actId: this.$route.query.id }).then(res => {
        this.signUpFormList = res.data.data;
        this.orginalData = item[0];
        this.dialogFormVisible = true;
      });
    },

    // 修改信息
    modifyData() {
      this.orginalData.status = this.selectedUser['状态'];
      delete this.selectedUser['状态'];
      delete this.selectedUser.undefined;
      this.orginalData.applyInfo = JSON.stringify(this.selectedUser);
      this.orginalData.message = '';
      this.modifyLoading = true;
      adminUpdateSignUpFromPc(this.orginalData).then(res => {
        if (res.data.status === 0) {
          this.$message.success(res.data.message);
        } else {
          this.$message.error('修改失败');
        }
        this.modifyLoading = false;
        this.dialogFormVisible = false;
        this.getSignUpListFromPc();
      });
    },

    // 审核通过
    pass(item) {
      if (item[0].statusText !== '审核中') {
        this.$message.warning(`${item[0].statusText}的信息不允许审核`);
        return;
      }
      const ids = item.map(info => info.id);
      this.auditing(ids, 1);
    },

    // 审核不通过
    fail(item) {
      if (item[0].statusText !== '审核中') {
        this.$message.warning(`${item[0].statusText}的信息不允许审核`);
        return;
      }
      const ids = item.map(info => info.id);
      this.auditing(ids, 0);
    },

    auditing(ids, ischeck) {
      auditSignUpFromPc({ signUpIdList: ids, actId: Number(this.$route.query.id), ischeck }).then(res => {
        if (res.data.status === 200) {
          this.$message.success(res.data.message);
        } else {
          this.$message.error('审核失败');
        }
        this.getSignUpListFromPc();
      });
    },

    // TODO 暂时未做删除
    deleteSignUp(item) {
      item[0].deleted = 1;
      deleteSignUpInfoFromPc(item[0]).then(res => {
        if (res.data.status === 0) {
          this.$message.success('删除成功');
        } else {
          this.$message.error('删除失败');
        }
        this.getSignUpListFromPc();
      });
    },

    // 开奖
    drawFromPc() {
      this.$confirm('确定现在开奖吗?', '', {
        type: 'warning'
      }).then(() => {
        drawFromPc({ actId: this.$route.query.id }).then(res => {
          if (res.data.status === 0) {
            this.$message.success('开奖成功');
            this.reload();
          } else {
            this.$message.error(res.data.message);
          }
        });
      }).catch(() => {});
    },

    // 编辑活动
    editActivity() {
      this.$router.push(`/Home/edit/${this.activityDetail.id}`);
    },

    // 删除活动
    activityControlFromPc(index) {
      this.$confirm(`确定${this.activityStatus[index]}活动吗?`, '', {
        type: 'warning'
      }).then(() => {
        deleteActivityFromPc({ actId: this.activityDetail.id, actName: this.activityDetail.actName, code: index }).then((res) => {
          if (res.data.status === 0) {
            this.$message.success(`${this.activityStatus[index]}活动成功`);
            if (index === 1) {
              this.$router.push('/Home/list');
            } else {
              this.reload();
            }
          } else {
            this.$message.error(`${this.activityStatus[index]}活动失败，请重试`);
          }
        });
      }).catch(() => {});
    },

    changeSignUpPageSize(args) {
      this.signUpPageSize = args;
      if (this.signUpPageIndex !== 1) {
        this.signUpPageIndex = 1;
      }
      this.getSignUpListFromPc(this.filterParams);
    },

    changeSignUpPageIndex(args) {
      this.signUpPageIndex = args;
      this.getSignUpListFromPc(this.filterParams);
    },

    changeLotteryPageSize(args) {
      this.lotteryPageSize = args;
      if (this.lotteryPageIndex !== 1) {
        this.lotteryPageIndex = 1;
      }
      this.getLotteryListFromPc();
    },

    changeLotteryPageIndex(args) {
      this.lotteryPageIndex = args;
      this.getLotteryListFromPc();
    },

    back() {
      this.$router.push('/Home/list');
    },

    // 获取我的报名信息
    async getMySignUpInfo() {
      const [err, res] = await this.request(getMySignUpFromMobile, {
        actId: this.$route.query.id
      });
      if (err) {
        return;
      }
      if (res.data.data !== undefined) {
        this.mySignUpInfo = res.data.data;
      }
    },

    // 获取我的报名状态文本
    getMySignUpStatusText() {
      if (!this.mySignUpInfo) return '未报名';
      const statusMap = {
        1: '报名成功',
        2: '审核中',
        3: '等待组队',
        4: '报名失败',
        5: '候补中',
        6: '活动人数已满'
      };
      return statusMap[this.mySignUpInfo.status] || '未知状态';
    },

    // 获取我的报名状态样式类
    getMySignUpStatusClass() {
      if (!this.mySignUpInfo) return 'status-not-signup';
      const classMap = {
        1: 'status-success',
        2: 'status-pending',
        3: 'status-waiting',
        4: 'status-failed',
        5: 'status-alternate',
        6: 'status-full'
      };
      return classMap[this.mySignUpInfo.status] || 'status-unknown';
    },

    // 打开报名对话框
    async openSignUpDialog() {
      // 获取报名字段
      const [err, res] = await this.request(getSignUpFieldFromPc, {
        actId: this.$route.query.id
      });
      if (err) {
        this.$message.error('获取报名字段失败');
        return;
      }
      this.signUpFormFields = res.data.data;

      // 初始化表单数据
      this.signUpFormData = {};
      this.signUpFormFields.forEach(field => {
        this.signUpFormData[field.fieldName] = '';
      });

      // 获取队伍列表（如果需要）
      if (this.activityDetail.activityDetail && this.activityDetail.activityDetail.isTeam > 0) {
        await this.getTeamList();
      }

      this.signUpDialogVisible = true;
    },

    // 获取队伍列表
    async getTeamList() {
      const [err, res] = await this.request(getTeamListFromMobile, {
        actId: this.$route.query.id,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      this.teamList = res.data.data || [];
    },

    // 提交报名
    async submitSignUp() {
      this.signUpLoading = true;
      try {
        const signUpData = {
          actId: this.$route.query.id,
          applyInfo: JSON.stringify(this.signUpFormData),
          isTeamLeader: 0 // PC端默认不是队长
        };

        const [err, res] = await this.request(signUpFromMobile, signUpData);
        if (err) {
          this.$message.error('报名失败');
          return;
        }

        const { message, status } = res.data;
        if (status === 0) {
          this.$message.success('报名成功');
          this.signUpDialogVisible = false;
          this.getMySignUpInfo();
          this.getSignUpListFromPc();
        } else if (status === -1 && message === '候补中') {
          this.$message.warning(message);
          this.signUpDialogVisible = false;
          this.getMySignUpInfo();
          this.getSignUpListFromPc();
        } else {
          this.$message.error(message);
        }
      } finally {
        this.signUpLoading = false;
      }
    },

    // 查看我的报名信息
    viewMySignUp() {
      if (this.mySignUpInfo && this.mySignUpInfo.applyInfo) {
        try {
          const applyInfo = JSON.parse(this.mySignUpInfo.applyInfo);
          this.signUpFormData = applyInfo;
          this.viewSignUpDialogVisible = true;
        } catch (e) {
          this.$message.error('报名信息格式错误');
        }
      }
    },

    // 取消报名
    cancelSignUp() {
      this.$confirm('确定取消报名吗？', '提示', {
        type: 'warning'
      }).then(async () => {
        const [err, res] = await this.request(deleteSignUpInfoFromMobile, this.mySignUpInfo);
        if (err) {
          this.$message.error('取消报名失败');
          return;
        }

        if (res.data.status === 0) {
          this.$message.success('取消报名成功');
          this.mySignUpInfo = null;
          this.getSignUpListFromPc();
        } else {
          this.$message.error(res.data.message);
        }
      }).catch(() => {});
    }
  }
};
</script>

<style lang="scss" scoped>
// 主容器样式
.activity-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

// 返回按钮样式
.back-button-wrapper {
  margin-bottom: 20px;

  .back-button {
    font-size: 16px;
    color: #606266;
    transition: all 0.3s ease;
    padding: 12px 20px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    &:hover {
      color: #409EFF;
      background: rgba(255, 255, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 20px rgba(64, 158, 255, 0.2);
    }

    i {
      margin-right: 8px;
      font-size: 18px;
    }
  }
}

// 活动信息卡片样式
.activity-info-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

// 统计卡片样式
.statistics-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }
}

// 报名卡片样式
.signup-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 24px;
  overflow: hidden;
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  .signup-actions {
    display: flex;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;

    .el-button {
      border-radius: 8px;
      padding: 12px 24px;
      font-weight: 600;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
    }
  }

  .signup-info {
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #409EFF;

    .info-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-weight: 600;
        color: #606266;
        margin-right: 8px;
        min-width: 120px;
      }

      .info-value {
        color: #303133;
        font-weight: 500;
      }
    }
  }
}

// 卡片头部样式
.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-content {
    display: flex;
    align-items: center;

    .header-icon {
      font-size: 24px;
      margin-right: 12px;
      opacity: 0.9;
    }

    .card-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .statistics-summary {
    display: flex;
    gap: 24px;

    .summary-item {
      text-align: center;

      .summary-label {
        display: block;
        font-size: 12px;
        opacity: 0.8;
        margin-bottom: 4px;
      }

      .summary-value {
        display: block;
        font-size: 24px;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// 卡片内容样式
.card-content {
  padding: 32px;
}

// 筛选标签样式
.filter-tabs-wrapper {
  margin-bottom: 24px;

  .modern-tabs {
    ::v-deep .el-tabs__header {
      margin-bottom: 0;
      background: #f8f9fa;
      border-radius: 12px;
      padding: 8px;
      border: none;
    }

    ::v-deep .el-tabs__nav-wrap {
      overflow: visible;

      &::after {
        display: none;
      }
    }

    ::v-deep .el-tabs__nav {
      border: none;
    }

    ::v-deep .el-tabs__item {
      height: auto;
      line-height: 1.5;
      padding: 12px 20px;
      margin-right: 8px;
      border: none;
      border-radius: 8px;
      background: transparent;
      color: #606266;
      font-weight: 500;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.1);
        color: #409EFF;
      }

      &.is-active {
        background: linear-gradient(135deg, #409EFF, #36a3f7);
        color: white;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        transform: translateY(-1px);
      }
    }
  }
}

// 现代化表格样式
.modern-table {
  ::v-deep .el-table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;

    th {
      background: linear-gradient(135deg, #f8f9fa, #e9ecef);
      color: #495057;
      font-weight: 600;
      border: none;
      padding: 16px 12px;

      .cell {
        font-size: 14px;
        letter-spacing: 0.3px;
      }
    }

    td {
      border: none;
      padding: 16px 12px;
      border-bottom: 1px solid #f1f3f4;

      .cell {
        font-size: 14px;
        color: #495057;
      }
    }

    tr {
      transition: all 0.2s ease;

      &:hover {
        background: rgba(64, 158, 255, 0.04);
        transform: scale(1.001);
      }
    }

    .el-table__empty-block {
      background: #fafbfc;
      border-radius: 8px;
      margin: 20px;
    }
  }

  // 分页器美化
  ::v-deep .el-pagination {
    margin-top: 24px;
    display: flex !important;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    gap: 8px;

    // 确保所有子元素水平排列
    > * {
      margin: 0 !important;
    }

    .el-pagination__sizes {
      order: 1;
      margin-right: 16px !important;

      .el-select {
        .el-input__inner {
          border-radius: 8px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;
          height: 36px;
          line-height: 36px;

          &:hover {
            border-color: #409EFF;
          }

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
          }
        }
      }
    }

    .btn-prev {
      order: 2;
      background: linear-gradient(135deg, #409EFF, #36a3f7);
      border: none;
      color: white;
      border-radius: 8px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }

      &:disabled {
        background: #e4e7ed;
        color: #c0c4cc;
        transform: none;
        box-shadow: none;
      }
    }

    .el-pager {
      order: 3;
      display: flex !important;
      align-items: center;

      li {
        background: white;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        margin: 0 4px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        font-weight: 500;

        &:hover {
          background: rgba(64, 158, 255, 0.1);
          border-color: #409EFF;
          color: #409EFF;
          transform: translateY(-1px);
        }

        &.active {
          background: linear-gradient(135deg, #409EFF, #36a3f7);
          border-color: #409EFF;
          color: white;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }

    .btn-next {
      order: 4;
      background: linear-gradient(135deg, #409EFF, #36a3f7);
      border: none;
      color: white;
      border-radius: 8px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }

      &:disabled {
        background: #e4e7ed;
        color: #c0c4cc;
        transform: none;
        box-shadow: none;
      }
    }

    .el-pagination__total {
      order: 5;
      color: #606266;
      font-weight: 500;
      margin-left: 16px !important;
    }

    .el-pagination__jump {
      order: 6;
      margin-left: 16px !important;

      .el-input__inner {
        border-radius: 8px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;
        height: 36px;
        line-height: 36px;

        &:hover {
          border-color: #409EFF;
        }

        &:focus {
          border-color: #409EFF;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }
      }
    }
  }
}

// 报名状态样式
.status-value {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.status-not-signup {
    background: rgba(144, 147, 153, 0.1);
    color: #909399;
  }

  &.status-success {
    background: rgba(103, 194, 58, 0.1);
    color: #67C23A;
  }

  &.status-pending {
    background: rgba(230, 162, 60, 0.1);
    color: #E6A23C;
  }

  &.status-waiting {
    background: rgba(64, 158, 255, 0.1);
    color: #409EFF;
  }

  &.status-failed {
    background: rgba(245, 108, 108, 0.1);
    color: #F56C6C;
  }

  &.status-alternate {
    background: rgba(144, 147, 153, 0.1);
    color: #909399;
  }

  &.status-full {
    background: rgba(245, 108, 108, 0.1);
    color: #F56C6C;
  }

  &.status-unknown {
    background: rgba(144, 147, 153, 0.1);
    color: #909399;
  }
}

// 查看报名信息对话框样式
.signup-info-view {
  .info-row {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-weight: 600;
      color: #606266;
      margin-right: 16px;
      min-width: 80px;
    }

    .info-value {
      color: #303133;
      flex: 1;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .activity-detail-container {
    padding: 12px;
  }

  .card-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;

    .statistics-summary {
      gap: 16px;
    }
  }

  .card-content {
    padding: 20px;
  }

  // 移动端分页器优化
  .modern-table {
    ::v-deep .el-pagination {
      flex-direction: column;
      gap: 12px;

      .el-pagination__sizes,
      .el-pagination__total,
      .el-pagination__jump {
        order: 0;
        margin: 0 !important;
      }

      .btn-prev,
      .el-pager,
      .btn-next {
        order: 1;
      }

      .el-pager {
        justify-content: center;
        flex-wrap: wrap;

        li {
          margin: 2px;
        }
      }
    }
  }
}</style>





