// ===== 响应式工具类 =====

// === 间距工具类 ===
@each $name, $value in (
  'xs': $spacing-xs,
  'sm': $spacing-sm,
  'base': $spacing-base,
  'lg': $spacing-lg,
  'xl': $spacing-xl,
  'xxl': $spacing-xxl,
  'xxxl': $spacing-xxxl
) {
  // Margin
  .m-#{$name} { margin: $value !important; }
  .mt-#{$name} { margin-top: $value !important; }
  .mr-#{$name} { margin-right: $value !important; }
  .mb-#{$name} { margin-bottom: $value !important; }
  .ml-#{$name} { margin-left: $value !important; }
  .mx-#{$name} { margin-left: $value !important; margin-right: $value !important; }
  .my-#{$name} { margin-top: $value !important; margin-bottom: $value !important; }
  
  // Padding
  .p-#{$name} { padding: $value !important; }
  .pt-#{$name} { padding-top: $value !important; }
  .pr-#{$name} { padding-right: $value !important; }
  .pb-#{$name} { padding-bottom: $value !important; }
  .pl-#{$name} { padding-left: $value !important; }
  .px-#{$name} { padding-left: $value !important; padding-right: $value !important; }
  .py-#{$name} { padding-top: $value !important; padding-bottom: $value !important; }
}

// === 文本工具类 ===
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }
.text-justify { text-align: justify !important; }

.text-primary { color: $color-text-primary !important; }
.text-secondary { color: $color-text-secondary !important; }
.text-tertiary { color: $color-text-tertiary !important; }
.text-disabled { color: $color-text-disabled !important; }

.text-success { color: $color-success !important; }
.text-warning { color: $color-warning !important; }
.text-error { color: $color-error !important; }
.text-info { color: $color-info !important; }

.text-xs { font-size: $font-size-xs !important; }
.text-sm { font-size: $font-size-sm !important; }
.text-base { font-size: $font-size-base !important; }
.text-lg { font-size: $font-size-lg !important; }
.text-xl { font-size: $font-size-xl !important; }
.text-xxl { font-size: $font-size-xxl !important; }
.text-xxxl { font-size: $font-size-xxxl !important; }

.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.text-ellipsis { @include text-ellipsis; }

// === Flexbox 工具类 ===
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-block { display: block !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }
.align-stretch { align-items: stretch !important; }

.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

// === 位置工具类 ===
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// === 宽度和高度工具类 ===
.w-full { width: 100% !important; }
.w-auto { width: auto !important; }
.h-full { height: 100% !important; }
.h-auto { height: auto !important; }

// === 圆角工具类 ===
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: $border-radius-sm !important; }
.rounded { border-radius: $border-radius-base !important; }
.rounded-lg { border-radius: $border-radius-lg !important; }
.rounded-xl { border-radius: $border-radius-xl !important; }
.rounded-full { border-radius: 50% !important; }

// === 阴影工具类 ===
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: $shadow-sm !important; }
.shadow { box-shadow: $shadow-base !important; }
.shadow-lg { box-shadow: $shadow-lg !important; }
.shadow-xl { box-shadow: $shadow-xl !important; }

// === 背景工具类 ===
.bg-primary { background-color: $color-bg-primary !important; }
.bg-secondary { background-color: $color-bg-secondary !important; }
.bg-tertiary { background-color: $color-bg-tertiary !important; }

// === 边框工具类 ===
.border { border: 1px solid $color-border-primary !important; }
.border-t { border-top: 1px solid $color-border-primary !important; }
.border-r { border-right: 1px solid $color-border-primary !important; }
.border-b { border-bottom: 1px solid $color-border-primary !important; }
.border-l { border-left: 1px solid $color-border-primary !important; }
.border-none { border: none !important; }

// === 响应式显示工具类 ===
@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    .d-#{$breakpoint}-none { display: none !important; }
    .d-#{$breakpoint}-block { display: block !important; }
    .d-#{$breakpoint}-flex { display: flex !important; }
    .d-#{$breakpoint}-inline-flex { display: inline-flex !important; }
  }
}

// === 响应式文本对齐 ===
@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    .text-#{$breakpoint}-left { text-align: left !important; }
    .text-#{$breakpoint}-center { text-align: center !important; }
    .text-#{$breakpoint}-right { text-align: right !important; }
  }
}

// === 过渡动画工具类 ===
.transition { transition: all 0.3s ease !important; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease !important; }
.transition-transform { transition: transform 0.3s ease !important; }
.transition-opacity { transition: opacity 0.3s ease !important; }

// === 鼠标悬停效果 ===
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: $shadow-lg;
  }
}

.hover-scale {
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

// === 滚动条样式 ===
.scrollbar-thin {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: $color-bg-tertiary;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: $color-border-primary;
    border-radius: 3px;
    
    &:hover {
      background: $color-text-tertiary;
    }
  }
}

// === 加载动画 ===
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -30px, 0); }
  70% { transform: translate3d(0, -15px, 0); }
  90% { transform: translate3d(0, -4px, 0); }
}

.animate-spin { animation: spin 1s linear infinite; }
.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }
