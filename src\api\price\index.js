import {
  getRequest
} from '../../assets/js/request';

import { baseUrl } from '../../config/constants';
import { sortData, sortLotteryData } from './translator';
// 根据活动id获取报名信息
export const getLotteryListFromPc = params => {
  return getRequest(`${baseUrl}/reward/public/getLottery.do?from=pc`, params).then(sortLotteryData);
};
export const drawFromPc = params => {
  return getRequest(`${baseUrl}/reward/draw.do?from=pc`, params);
};
export const exportToExcel = params => {
  return getRequest(`${baseUrl}/reward/exportToExel.do`, params);
};
// 获取奖品信息
export const getRewardListFromPc = params => {
  return getRequest(`${baseUrl}/reward/public/getReward.do?from=pc`, params).then(sortData);
};

export const getRewardListFromMobile = params => {
  return getRequest(`${baseUrl}/reward/public/getReward.do?from=mobile`, params).then(sortData);
};
export const getLotteryListFromMobile = params => {
  return getRequest(`${baseUrl}/reward/public/getLottery.do?from=mobile`, params);// .then(sortLotteryData);
};
