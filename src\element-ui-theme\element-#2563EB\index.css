/* Element UI 现代美化主题 #2563EB */

/* === 主要按钮 === */
.el-button--primary {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border-color: #2563eb;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.el-button--primary:hover,
.el-button--primary:focus {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border-color: #3b82f6;
  color: #FFF;
  box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
  transform: translateY(-1px);
}

.el-button--primary:active {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  border-color: #1d4ed8;
  color: #FFF;
  transform: translateY(0);
}

.el-button--primary.is-active {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  border-color: #1d4ed8;
  color: #FFF;
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:hover,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:active {
  color: #FFF;
  background: #93c5fd;
  border-color: #93c5fd;
  box-shadow: none;
  transform: none;
}

.el-button--primary.is-plain {
  color: #2563eb;
  background: #eff6ff;
  border-color: #93c5fd;
}

.el-button--primary.is-plain:hover,
.el-button--primary.is-plain:focus {
  background: #2563eb;
  border-color: #2563eb;
  color: #FFF;
}

/* === 次要按钮 === */
.el-button--success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-color: #10b981;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.el-button--success:hover,
.el-button--success:focus {
  background: linear-gradient(135deg, #34d399 0%, #10b981 100%);
  border-color: #34d399;
  box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
  transform: translateY(-1px);
}

.el-button--warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

.el-button--warning:hover,
.el-button--warning:focus {
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  border-color: #fbbf24;
  box-shadow: 0 4px 8px rgba(245, 158, 11, 0.3);
  transform: translateY(-1px);
}

.el-button--danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border-color: #ef4444;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.el-button--danger:hover,
.el-button--danger:focus {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  border-color: #f87171;
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

/* === 链接 === */
.el-link.el-link--primary {
  color: #2563eb;
}

.el-link.el-link--primary:hover {
  color: #3b82f6;
}

/* === 表单组件 === */
.el-radio__input.is-checked .el-radio__inner {
  border-color: #2563eb;
  background: #2563eb;
}

.el-radio__input.is-checked + .el-radio__label {
  color: #2563eb;
}

.el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #2563eb;
  border-color: #2563eb;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: #2563eb;
}

.el-input__inner:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.el-select .el-input.is-focus .el-input__inner {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

.el-select-dropdown__item.selected {
  color: #2563eb;
  font-weight: 600;
}

/* === 开关和滑块 === */
.el-switch.is-checked .el-switch__core {
  border-color: #2563eb;
  background-color: #2563eb;
}

.el-slider__bar {
  background-color: #2563eb;
}

.el-slider__button {
  border-color: #2563eb;
}

.el-slider__button:hover,
.el-slider__button.hover,
.el-slider__button:focus {
  border-color: #3b82f6;
}

/* === 导航组件 === */
.el-menu--horizontal > .el-menu-item.is-active {
  border-bottom: 2px solid #2563eb;
  color: #2563eb;
}

.el-menu--vertical > .el-menu-item.is-active {
  background-color: #eff6ff;
  color: #2563eb;
}

.el-tabs__active-bar {
  background-color: #2563eb;
}

.el-tabs__item.is-active {
  color: #2563eb;
}

/* === 反馈组件 === */
.el-progress-bar__inner {
  background-color: #2563eb;
}

.el-progress__text {
  color: #2563eb;
}

.el-tag--primary {
  background-color: #eff6ff;
  border-color: #93c5fd;
  color: #2563eb;
}

.el-tag--success {
  background-color: #f0fdf4;
  border-color: #86efac;
  color: #10b981;
}

.el-tag--warning {
  background-color: #fffbeb;
  border-color: #fde68a;
  color: #f59e0b;
}

.el-tag--danger {
  background-color: #fef2f2;
  border-color: #fca5a5;
  color: #ef4444;
}

/* === 对话框和抽屉 === */
.el-dialog__header {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: #fff;
  border-radius: 8px 8px 0 0;
  padding: 20px 24px;
}

.el-dialog__title {
  color: #fff;
  font-weight: 600;
}

.el-dialog__close {
  color: #fff;
  font-size: 18px;
}

.el-dialog__close:hover {
  color: #dbeafe;
}

.el-drawer__header {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  color: #fff;
  padding: 20px 24px;
}

/* === 分页 === */
.el-pager li.active {
  background-color: #2563eb;
  color: #FFF;
  border-radius: 6px;
}

.el-pager li:hover {
  color: #2563eb;
}

/* === 步骤条 === */
.el-step__head.is-process {
  color: #2563eb;
  border-color: #2563eb;
}

.el-step__head.is-finish {
  color: #10b981;
  border-color: #10b981;
}

.el-step__main .el-step__title.is-process {
  color: #2563eb;
  font-weight: 600;
}

.el-step__main .el-step__title.is-finish {
  color: #10b981;
}

/* === 加载和骨架屏 === */
.el-loading-spinner .circular {
  stroke: #2563eb;
}

.el-skeleton__item {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 37%, #f3f4f6 63%);
}

/* === 现代化增强 === */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-input__inner {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-card {
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.el-message-box {
  border-radius: 12px;
}

.el-notification {
  border-radius: 12px;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}
