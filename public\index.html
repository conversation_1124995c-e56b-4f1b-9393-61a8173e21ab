<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= htmlWebpackPlugin.options.title %></title>
    <script>
        (function (win, doc) {
          var html = document.documentElement;
          function setFont() {
            var clientWidth = html.clientWidth;
            var rem;

            // 只在移动端设备上使用动态rem计算
            if (clientWidth <= 768) {
              // 移动端：基于375px设计稿，1rem = 37.5px
              rem = clientWidth / 10;
              // 限制最小和最大值
              rem = Math.max(32, Math.min(rem, 42));
            } else {
              // PC端：使用固定的基础字体大小
              rem = 16; // 标准的浏览器默认字体大小
            }

            html.style.fontSize = rem + 'px';
          }
          window.addEventListener('resize', setFont, false);
          window.addEventListener('DOMContentLoaded', setFont, false);
        })(window, document)
    </script>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="mobile"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
