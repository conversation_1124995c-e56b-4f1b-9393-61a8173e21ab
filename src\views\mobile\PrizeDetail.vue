<template>
  <div class="container">
    <van-nav-bar
      title="奖品详情"
      left-text="返回"
      left-arrow
      @click-left="onBack"
    />
    <div class="content">
      <div class="privew">
        <div class="title">
          <span class="decorate"></span>
          <span> 奖品预览 </span>
        </div>
        <van-divider />
        <my-table :thead="rewardThead" :data="rewardData" border />
      </div>
      <div class="list" >
        <div class="title">
          <span class="decorate"></span>
          <span> 中奖名单 </span>
        </div>
        <van-divider />
        <my-table
          v-if="winnersData.length !== 0"
          :thead="winnersThead"
          :data="winnersData"
          border
        />
        <p style="color:grey;flex:1;text-align:center;height: 100px; line-height: 100px" v-else>暂未开奖</p>
      </div>

    </div>
  </div>
</template>

<script>
import MyTable from '@/components/Table.vue';
import { getRewardListFromMobile, getLotteryListFromMobile } from '../../api/price/index';
import requestMixin from '../../mixin/requestMixin';
export default {
  components: {
    MyTable
  },
  mixins: [requestMixin],
  data() {
    return {
      rewardThead: [
        {
          prop: 'rewardLevel',
          label: '奖项'
        },
        {
          prop: 'rewardName',
          label: '奖励'
        },
        {
          prop: 'number',
          label: '数量'
        }
      ],
      rewardData: [],
      winnersThead: [
        {
          prop: 'rewardLevel',
          label: '奖项'
        },
        {
          prop: 'name',
          label: '姓名'
        }
      ],
      winnersData: []
    };
  },
  created() {
    if (!this.$route.query.actId) {
      this.$router.push('/list');
    } else {
      this.getRewardListFromMobile();
      this.getLotteryListFromMobile();
    }
  },
  methods: {
    onBack() {
      this.$router.go(-1);
    },
    // 根据活动id获取奖品详情
    async getRewardListFromMobile() {
      const [err, res] = await this.request(getRewardListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      this.rewardData = res.data.data;
    },
    // 根据活动id获取中奖名单
    async getLotteryListFromMobile() {
      const [err, res] = await this.request(getLotteryListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      this.winnersData = res.data.data;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  background: #f1f2f3;
  min-height: 100vh;
  .content {
    font-size: px2rem(14px);
    padding: px2rem(10px);
    .privew {
      background: #fff;
      padding: px2rem(10px);
      box-sizing: border-box;
    }
    .list {
      @extend .privew;
      margin-top: px2rem(10px);
    }
  }
}

.title {
  display: flex;
  align-items: flex-end;
  height: px2rem(20px);
  .decorate {
    display: inline-block;
    width: px2rem(4px);
    height: 100%;
    background: $promary-color;
    margin-right: px2rem(8px);
  }
}
</style>
