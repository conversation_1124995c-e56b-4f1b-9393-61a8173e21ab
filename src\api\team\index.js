import { getRequest } from '../../assets/js/request';

import { baseUrl } from '../../config/constants';
import { addField, translate, sortData } from './translate';

// 根据活动id查询队长信息
export const getTeamLeaderFromPc = params => {
  return getRequest(`${baseUrl}/team/getTeamLeader.do?from=pc`, params);
};

// 根据活动id获取报名信息
export const getTeamListFromMobile = params => {
  return getRequest(`${baseUrl}/team/public/getTeam.do?from=mobile`, params)
  // return getRequest(`${baseUrl}/team/public/getTeamList.do?from=mobile`, params)
    .then(addField)
    .then(sortData);
};
// 根据队伍id查询对内成员
export const getTeamDetailFromMobile = params => {
  return getRequest(
    `${baseUrl}/signUp/public/getPlayer.do?from=mobile`,
    params
  ).then(translate);
};
