<template>
  <el-form :model="field" :rules="rules" ref="priceForm">
    <el-form-item label="奖项" prop="rewardLevel">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input v-model="field.rewardLevel" />
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="奖品" prop="rewardName">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input v-model="field.rewardName" />
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item label="份数" prop="number">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-input v-model.number="field.number" type="number" />
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  props: {
    priceField: {
      type: Object,
      require: true
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      // 拷贝，防止同步修改
      field: JSON.parse(JSON.stringify(this.priceField))
    };
  },
  methods: {
    validate() {
      let status = false;
      this.$refs.priceForm.validate(valid => {
        if (valid) {
          status = true;
        }
      });
      return status;
    },
    returnField() {
      return this.field;
    }
  }
};
</script>

<style>

</style>
