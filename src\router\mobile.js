import Vue from 'vue';
import VueRouter from 'vue-router';
import Home from '@/views/mobile/Home.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    redirect: '/activityList',
    children: [
      {
        path: 'activityList',
        component: () => import('@/views/mobile/activity/ActivityList.vue'),
        meta: { title: '活动列表' }
      },
      {
        path: 'activityDetail',
        component: () => import('@/views/mobile/activity/ActivityDetail.vue'),
        meta: { title: '活动详情' }
      },
      {
        path: 'prizeDetail',
        component: () => import('@/views/mobile/PrizeDetail.vue'),
        meta: { title: '奖品详情' }
      },
      {
        path: 'signup',
        component: () => import('@/views/mobile/SignUp.vue'),
        meta: { title: '报名信息' }
      }
    ]
  }
];

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes,
  scrollBehavior() {
    return { x: 0, y: 0 };
  }
});

export default router;
