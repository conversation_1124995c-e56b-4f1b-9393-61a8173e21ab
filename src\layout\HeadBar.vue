<template>
  <div class="modern-head-bar">
    <!-- 左侧品牌区域 -->
    <div class="brand-section">
      <div class="brand-icon">
       <img src="../assets/images/logo.png" alt="品牌图标" class="custom-icon">
      </div>
      <div class="brand-text">
        <h1>活动发布系统</h1>
      </div>
    </div>

    <!-- 中间导航区域 -->
    <div class="nav-section">
      <div class="nav-breadcrumb">
        <span class="breadcrumb-item">{{ getCurrentPageTitle() }}</span>
      </div>

      <!-- 快捷操作按钮 -->
      <div class="quick-actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          class="create-activity-btn"
          @click="createActivity"
          size="medium"
        >
          创建活动
        </el-button>
        <el-button
          icon="el-icon-refresh"
          class="refresh-btn"
          @click="refreshPage"
          size="medium"
          circle
        ></el-button>
      </div>
    </div>

    <!-- 右侧用户区域 -->
    <div class="user-section">
      <!-- 通知按钮 -->
      <div class="notification-btn" @click="showNotifications">
        <i class="el-icon-bell"></i>
        <span class="notification-badge" v-if="notificationCount > 0">{{ notificationCount }}</span>
      </div>

      <!-- 用户下拉菜单 -->
      <div class="user-dropdown">
        <div class="user-info" @click="toggleUserMenu">
          <img class="user-avatar" src="@/assets/images/avator.gif" alt="user-avatar">
          <div class="user-details">
            <span class="user-name">管理员</span>
            <span class="user-role">Administrator</span>
          </div>
          <i class="el-icon-arrow-down dropdown-arrow"></i>
        </div>

        <el-dropdown trigger="click" @command="handleCommand" placement="bottom-end">
          <span class="dropdown-trigger"></span>
          <el-dropdown-menu slot="dropdown" class="modern-dropdown">
            <el-dropdown-item command="profile" class="dropdown-item">
              <i class="el-icon-user"></i>
              <span>个人资料</span>
            </el-dropdown-item>
            <el-dropdown-item command="sync" class="dropdown-item">
              <i class="el-icon-refresh"></i>
              <span>同步通讯录</span>
            </el-dropdown-item>
            <el-dropdown-item command="changePass" class="dropdown-item">
              <i class="el-icon-key"></i>
              <span>修改密码</span>
            </el-dropdown-item>
            <el-dropdown-item divided command="logout" class="dropdown-item logout">
              <i class="el-icon-switch-button"></i>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>

    <!-- 装饰性元素 -->
    <div class="header-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
    </div>
    <el-dialog
            title="修改密码"
            width="30%"
            :visible.sync="dialogTableVisible"
            class="pwd"
            :close-on-click-modal="false">
            <el-form
              :model="pwdFormData"
              :rules="rules"
              ref="pwdForm"
              label-width="70px">
              <el-form-item label="新密码:" prop="newPwd">
                <el-input
                  type="password"
                  size="small"
                  v-model="pwdFormData.newPwd"
                  maxlength="16"
                  spellcheck ="false"
                  clearable
                  show-password/>
              </el-form-item>
            </el-form>
            <template #footer>
              <span class="dialog-footer">
                <el-button type="info" size="small" @click="dialogTableVisible=false" >取 消</el-button>
                <el-button type="primary" size="small" @click="submitForm('pwdForm')">确 定</el-button>
              </span>
            </template>
    </el-dialog>
  </div>
</template>
<script>
import { syncAddressList } from '../api/activity/index';
import { LogoutPc, ChangePwdPc } from '../api/Login/index';
export default {
  name: 'HeadBar',
  data() {
    return {
      // 修改密码弹窗
      dialogTableVisible: false,
      pwdFormData: {
        newPwd: ''
      },
      rules: {
        newPwd: [{
          required: true,
          min: 6,
          max: 16,
          message: '长度在6到16个字符',
          trigger: 'blur'
        }]
      }
    };
  },
  created() {},
  beforeDestroy() {},
  methods: {
    handleCommand(command) {
      // if (command === 'signOut') {
      //   this.$store.commit('setAuthorization', '');
      //   this.$router.options.isAddDynamicRoutes = false;
      //   this.$router.push('/login');
      // }
      // this.$store.commit('initCachedViews');
      if (command === 'sync') {
        syncAddressList().then(res => {
          if (res.data.status === 0) {
            this.$message.success('通讯录同步成功');
          } else {
            this.$message.error('通讯录同步失败');
          }
        });
      } else if (command === 'logout') {
        this.Logout();
      } else if (command === 'changePass') {
        this.dialogTableVisible = true;
      }
    },
    Logout() {
      LogoutPc().then((res) => {
        if (res.data.status === 200) {
          this.$message.success('退出登录成功');
          window.sessionStorage.removeItem('userInfo');
          this.$router.push('/Login');
        } else {
          this.$message.error('退出登录失败');
        }
      });
    },
    ChangeLogout() {
      LogoutPc().then((res) => {
        if (res.data.status === 200) {
          window.sessionStorage.removeItem('userInfo');
          setTimeout(() => { this.$router.push('/Login'); }, 1000);
        } else {
        }
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          ChangePwdPc({
            newPassword: this.pwdFormData.newPwd
          }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success('密码修改成功，需重新登录');
              this.dialogTableVisible = false;
              this.pwdFormData.newPwd = ''; // 清空表单
              this.ChangeLogout();
            } else {
              this.$message.error(res.data.message || '修改失败');
            }
          }).catch((error) => {
            console.error('修改密码失败:', error);
            this.$message.error('修改失败，请稍后重试');
          });
        } else {
          this.$message({
            type: 'error',
            message: '格式错误，请重新填写'
          });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.modern-head-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  // 移动端适配 - 减少内边距
  @media screen and (max-width: 768px) {
    height: 50px;
    padding: 0 12px;
  }

  // 左侧品牌区域
  .brand-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;

    // 移动端隐藏logo以节省空间
    @media screen and (max-width: 768px) {
      .brand-icon {
        display: none;
      }
    }

    // 小屏幕时进一步优化
    @media screen and (max-width: 480px) {
      gap: 8px;
    }

    .brand-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);

      .custom-icon {
        width: 28px;
        height: 28px;
        object-fit: contain;
      }
    }

    .brand-text {
      h1 {
        color: #ffffff;
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        letter-spacing: 0.5px;

        // 移动端字体大小优化
        @media screen and (max-width: 768px) {
          font-size: 16px;
        }

        @media screen and (max-width: 480px) {
          font-size: 14px;
        }
      }
    }
  }

  // 中间导航区域
  .nav-section {
    display: flex;
    align-items: center;
    gap: 20px;
    flex: 1;
    justify-content: center;

    // 移动端隐藏导航区域以节省空间
    @media screen and (max-width: 768px) {
      display: none;
    }

    .nav-breadcrumb {
      .breadcrumb-item {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        font-weight: 500;
      }
    }

    .quick-actions {
      display: flex;
      gap: 8px;

      .create-activity-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #ffffff;
        font-weight: 500;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }
      }

      .refresh-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #ffffff;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-1px);
        }
      }
    }
  }

  // 右侧用户区域
  .user-section {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-shrink: 0;

    // 移动端优化间距
    @media screen and (max-width: 768px) {
      gap: 12px;
    }

    @media screen and (max-width: 480px) {
      gap: 8px;
    }

    .notification-btn {
      position: relative;
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);

      // 移动端尺寸优化
      @media screen and (max-width: 768px) {
        width: 32px;
        height: 32px;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-1px);
      }

      i {
        color: #ffffff;
        font-size: 16px;

        @media screen and (max-width: 768px) {
          font-size: 14px;
        }
      }

      .notification-badge {
        position: absolute;
        top: -4px;
        right: -4px;
        background: #ff4757;
        color: #ffffff;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        line-height: 1;
      }
    }

    .user-dropdown {
      position: relative;

      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 6px 12px;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        // 移动端简化显示
        @media screen and (max-width: 768px) {
          padding: 4px 8px;
          gap: 6px;
        }

        @media screen and (max-width: 480px) {
          .user-details {
            display: none;
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .user-avatar {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          object-fit: cover;
          border: 2px solid rgba(255, 255, 255, 0.3);

          @media screen and (max-width: 768px) {
            width: 28px;
            height: 28px;
          }
        }

        .user-details {
          display: flex;
          flex-direction: column;
          gap: 2px;

          .user-name {
            color: #ffffff;
            font-size: 13px;
            font-weight: 600;
            line-height: 1;
          }

          .user-role {
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            line-height: 1;
          }
        }

        .dropdown-arrow {
          color: rgba(255, 255, 255, 0.7);
          font-size: 12px;
          transition: transform 0.3s ease;

          @media screen and (max-width: 480px) {
            display: none;
          }
        }
      }
    }
  }

  // 装饰性元素
  .header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;

    // 移动端隐藏装饰元素
    @media screen and (max-width: 768px) {
      display: none;
    }

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.05);

      &.circle-1 {
        width: 120px;
        height: 120px;
        top: -60px;
        right: 100px;
        animation: float 6s ease-in-out infinite;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        top: -40px;
        right: 250px;
        animation: float 8s ease-in-out infinite reverse;
      }
    }
  }
}

// 下拉菜单样式
.modern-dropdown {
  background: #ffffff;
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  margin-top: 8px;

  .dropdown-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 20px;
    color: #2d3436;
    font-size: 14px;
    transition: all 0.3s ease;

    &:hover {
      background: #f8f9fa;
      color: #667eea;
    }

    &.logout {
      color: #e74c3c;

      &:hover {
        background: #ffeaea;
        color: #c0392b;
      }
    }

    i {
      font-size: 16px;
      width: 16px;
      text-align: center;
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// 兼容旧样式
.head-bar {
  box-sizing: border-box;
  height: 60px;
  padding: 14px 46px 9px 20px;
  background: linear-gradient(to right, #14babc, #0e95b0);
  background: #e65151;
  user-select: none;
  .title {
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    line-height: 33px;
    display: flex;
    align-items: center;
    .fa {
      font-size: 34px;
      margin: 0 20px;
    }
  }
  .user-avatar {
      width: 36px;
      border-radius: 25%;
  }
}
.fl {
  float: left;
}
.fr {
  cursor: pointer;
  float: right;
  deep .el-dropdown {
    vertical-align: bottom;
    color: #FFF;
  }
}
.pwd >>> .el-form-item__label{
  color: black;
}
</style>
