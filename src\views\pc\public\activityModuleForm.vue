<template>
  <div class="form">
    <el-form
      ref="activityModuleForm"
      label-width="50px"
    >
      <el-form-item v-for="(item, index) in activityModuleList" :key="index">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form
              style="
                border: 1px solid #dcdfe6;
                padding: 20px 40px;
                border-radius: 10px;
              "
              :model="item"
              ref="moduleForm"
              :rules='rules'
            >
              <el-form-item>
                <el-row>
                  <el-col>
                    <i class="el-icon-close" @click="deleteActivity(index)" ></i>
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="标题" prop="title">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-input v-model="item.title" />
                  </el-col>
                </el-row>
              </el-form-item>
              <el-form-item label="内容" prop="content" style="marginTop:20px">
                <el-row :gutter="20">
                  <el-col :span="16">
                    <vue-editor
                      v-model="item.content"
                      :editorToolbar="customToolbar"
                    />
                  </el-col>
                </el-row>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addActivity" size="small">
          <i class="el-icon-plus"></i> 添加活动内容
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { VueEditor } from 'vue2-editor';
export default {
  components: {
    VueEditor
  },
  props: {
    activityModuleList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      // 自定义工具栏
      customToolbar: [
        // 字体
        [{ header: [] }],
        ['bold', 'italic', 'underline'],
        // 对齐方式
        [{ align: '' }, { align: 'center' }, { align: 'right' }],
        // 缩进
        [{ indent: '-1' }, { indent: '+1' }],
        // 排序方式
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ background: [] }, { color: [] }],
        ['image', 'link'],
        ['strike'],
        ['clean']
      ],
      rules: {
        title: [
          { required: true, message: '请输入活动内容标题', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入活动内容具体介绍', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    addActivity() {
      this.$emit('addActivityModule');
      // this.$refs.moduleForm[0].validateField('content');
    },
    deleteActivity(item) {
      this.$emit('deleteActivityModule', item);
    },
    verify() {
      let status = true;
      // 只记录一次状态的改变,但是要遍历触发所有的校验
      let id = 0;
      this.activityModuleList.forEach((_, index) => {
        // 由于vue-editor不能通过blur触发校验，则采用手动触发
        this.$refs.moduleForm[index].validateField('content', err => {
          if (id === 0 && err) {
            status = false;
            id++;
          }
        });
        this.$refs.moduleForm[index].validateField('title', err => {
          if (id === 0 && err) {
            status = false;
            id++;
          }
        });
      });
      return status;
    }
  }
};
</script>

<style scoped>
.form {
  margin-top: 40px;
}
.el-icon-close{
  font-size: 20px;
  float: right;
  color: grey;
  cursor: pointer;
}
</style>
