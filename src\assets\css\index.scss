@import "reset";      // 全局重置样式
@import "variables";  // 设计系统变量
@import "utilities";  // 工具类
@import "components"; // 通用组件样式
@import "responsive"; // 响应式适配样式
@import "vant-pc-adapter"; // Vant 组件 PC 端适配
@import "editor";     // 编辑器样式

// Element UI 消息提示位置调整
.el-message {
  top: 60px !important; // 默认是20px，下移40px后为60px
}

// Element UI 弹窗层级修复 - 确保弹窗不被遮挡
.el-dialog__wrapper {
  z-index: 3000 !important;
}

.el-overlay {
  z-index: 1999 !important;
}

.v-modal {
  z-index: 1999 !important;
}

// 强制降低所有可能的遮罩层
.el-popup-parent--hidden {
  z-index: 1999 !important;
}

.el-loading-mask {
  z-index: 1999 !important;
}

// MessageBox 确认弹窗层级设置
.el-message-box__wrapper {
  z-index: 3001 !important;
}

.el-message-box {
  z-index: 3002 !important;
}

.el-message-box__wrapper .v-modal {
  z-index: 1998 !important;
}

// 确保所有弹窗相关元素都有足够高的层级
.el-dialog {
  z-index: 3000 !important;
}

.el-overlay-dialog {
  z-index: 3000 !important;
}

// 强制覆盖slideMenu菜单背景色 - 使用最高优先级
.modern-slide-menu .el-menu {
  background: transparent !important; // 主菜单透明背景，继承父组件渐变
}

.modern-slide-menu .el-menu.el-menu--inline {
  background: rgba(37, 99, 235, 0.1) !important; // 子菜单主题色背景
}

.modern-slide-menu .el-menu.el-menu--inline .el-menu-item:hover {
  background: rgba(37, 99, 235, 0.2) !important;
}

.modern-slide-menu .el-menu.el-menu--inline .el-menu-item.is-active {
  background: rgba(37, 99, 235, 0.3) !important;
}
