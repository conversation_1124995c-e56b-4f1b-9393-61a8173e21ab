<template>
  <div class="activity-list-page">

    <!-- 筛选导航 -->
    <div class="filter-section">
      <Navigator
        @handleSelect="handleSelect"
        :activeIndex="activeIndex"
        class="modern-navigator"
      />
    </div>

    <!-- 活动列表 -->
    <div class="content-section">
      <div
        class="activity-grid"
        v-loading="loading"
        element-loading-text="加载中..."
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.05)"
      >
        <ActivityCard
          :activityList="activityList"
          @refresh="refreshData"
        />

        <!-- 空状态 -->
        <div v-if="!loading && activityList.length === 0" class="empty-state">
          <div class="empty-icon">
            <i class="el-icon-document-delete"></i>
          </div>
          <h3 class="empty-title">暂无活动</h3>
          <p class="empty-description">
            {{ getEmptyDescription() }}
          </p>
          <el-button
            type="primary"
            @click="createActivity"
            icon="el-icon-plus"
          >
            创建第一个活动
          </el-button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section" v-if="totalPage > 0">
      <el-pagination
        class="modern-pagination"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalPage"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import Navigator from './Navigator';
import ActivityCard from '../../../components/activity-card';
import { getActivityListFromPc } from '../../../api/activity/index';
import requestMixin from '../../../mixin/requestMixin';
import { convertTimeWithMinutes } from '../../../utils/index';
export default {
  components: {
    Navigator,
    ActivityCard
  },
  mixins: [requestMixin],
  data() {
    return {
      activityList: [],
      activeIndex: '5', // 默认显示所有活动
      totalPage: 0,
      currentPage: 1,
      pageSize: 10,
      loading: false,
      // 统计数据
      totalActivities: 0,
      activeActivities: 0,
      signupActivities: 0
    };
  },
  created() {
    let page = 1;
    if (this.$store.state.currentPage !== null) {
      page = this.$store.state.currentPage;
      this.currentPage = page;
    } else {
      this.$store.commit('setCurrentPage', 1);
    }
    this.getActivityListFromPc('5', page); // 默认加载所有活动
  },
  methods: {
    handleSelect(type) {
      this.activeIndex = type;
      this.currentPage = 1; // 重置页码
      this.loading = true;
      this.getActivityListFromPc(this.activeIndex, 1);
    },

    async getActivityListFromPc(flag, currentPage) {
      const params = {
        createTime: convertTimeWithMinutes(new Date().getTime()),
        pageSize: currentPage,
        flag,
        pageNumber: this.pageSize
      };
      const [err, res] = await this.request(getActivityListFromPc, params);
      this.loading = false;
      if (err) {
        this.$message.error('获取活动列表失败');
        return;
      }
      this.activityList = res.data.data.records || [];
      this.totalPage = res.data.data.total || 0;

      // 调试：打印活动数据
      console.log('活动列表数据:', this.activityList);
      if (this.activityList.length > 0) {
        console.log('第一个活动的图片字段:', {
          actImg: this.activityList[0].actImg,
          activityImage: this.activityList[0].activityImage,
          image: this.activityList[0].image,
          img: this.activityList[0].img
        });
      }

      // 更新统计数据
      this.updateStats();
    },

    handlePageChange(page) {
      this.currentPage = page;
      this.$store.commit('setCurrentPage', page);
      this.loading = true;
      this.getActivityListFromPc(this.activeIndex, page);
    },

    handleSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1;
      this.loading = true;
      this.getActivityListFromPc(this.activeIndex, 1);
    },

    createActivity() {
      this.$router.push('/Home/public');
    },

    refreshData() {
      this.loading = true;
      this.getActivityListFromPc(this.activeIndex, this.currentPage);
    },

    getEmptyDescription() {
      return '系统中暂时没有任何活动';
    },

    updateStats() {
      // 这里可以根据实际需求更新统计数据
      // 目前使用模拟数据
      this.totalActivities = this.activityList.length;
      this.activeActivities = this.activityList.filter(item => item.status === 4).length;
      this.signupActivities = this.activityList.filter(item => item.status === 2).length;
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.activity-list-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-top:16px;

  // 页面头部
  .page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 32px 24px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        .page-title {
          font-size: 28px;
          font-weight: 700;
          margin: 0 0 8px 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .page-description {
          font-size: 16px;
          opacity: 0.9;
          margin: 0;
        }
      }

      .header-right {
        ::v-deep .el-button {
          padding: 12px 24px;
          font-size: 16px;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
          }
        }
      }
    }
  }

  // 筛选区域
  .filter-section {
    background: #fff;
    padding: 24px;
    margin: 0px 24px 24px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 1;

    .modern-navigator {
      margin-bottom: 24px;
    }
  }

  // 内容区域
  .content-section {
    padding: 0 24px;

    .activity-grid {
      position: relative;
      background: transparent;
      border-radius: 12px;
      padding: 24px;
    }

    // 空状态
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 80px 40px;
      text-align: center;
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
      border-radius: 16px;
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
      margin: 40px auto;
      max-width: 500px;
      height: 500px;
      .empty-icon {
        font-size: 64px;
        color: #c0c4cc;
        margin-bottom: 24px;
        opacity: 0.8;
      }

      .empty-title {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
      }

      .empty-description {
        font-size: 14px;
        color: #909399;
        margin: 0 0 32px 0;
        max-width: 300px;
        line-height: 1.5;
      }

      ::v-deep .el-button {
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }

  // 分页区域
  .pagination-section {
    padding: 32px 24px;
    display: flex;
    justify-content: center;

    .modern-pagination {
      background: rgba(255, 255, 255, 0.9);
      backdrop-filter: blur(20px);
      border-radius: 12px;
      padding: 16px 24px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.2);

      ::v-deep .el-pagination {
        .el-pager li {
          border-radius: 8px;
          margin: 0 4px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(64, 158, 255, 0.1);
            border-color: #409EFF;
            color: #409EFF;
            transform: translateY(-1px);
          }

          &.active {
            background: linear-gradient(135deg, #409EFF, #36a3f7);
            border-color: #409EFF;
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }
        }

        .btn-prev,
        .btn-next {
          border-radius: 8px;
          margin: 0 4px;
          border: 1px solid #e4e7ed;
          transition: all 0.3s ease;

          &:hover {
            background: linear-gradient(135deg, #409EFF, #36a3f7);
            border-color: #409EFF;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
          }

          &:disabled {
            background: #f5f7fa;
            color: #c0c4cc;
            transform: none;
            box-shadow: none;
          }
        }

        .el-pagination__sizes {
          .el-select .el-input .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409EFF;
            }

            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-pagination__jump {
          .el-input .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e4e7ed;
            transition: all 0.3s ease;

            &:hover {
              border-color: #409EFF;
            }

            &:focus {
              border-color: #409EFF;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
            }
          }
        }

        .el-pagination__total {
          color: #606266;
          font-weight: 500;
        }
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .activity-list-page {
    .page-header {
      padding: 24px 16px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        .header-left {
          .page-title {
            font-size: 24px;
          }

          .page-description {
            font-size: 14px;
          }
        }
      }
    }

    .filter-section {
      margin: -12px 16px 16px;
      padding: 16px;
    }

    .content-section {
      padding: 0 16px;
    }

    .pagination-section {
      padding: 24px 16px;

      .modern-pagination {
        ::v-deep .el-pagination {
          .el-pagination__sizes,
          .el-pagination__jump {
            display: none;
          }
        }
      }
    }
  }
}
</style>
