<template>
  <div class="activity-list">
    <el-dialog
      title="二维码"
      width="22%"
      :visible.sync="dialogVisible"
      :before-close="handleClose"
      class="qr-dialog"
      :close-on-click-modal="false"
    >
      <div align="center">
        <div class="qr-code-download">
          <div class="img">
            <vue-qr
              :text="Url"
              :margin="10"
              :size="200"
              :dotScale="qrCodeConfig.dotScale"
              :colorDark="qrCodeConfig.colorDark"
            />
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="downloadImg">下 载</el-button>
          <el-button
            type="primary"
            @click="dialogVisible = false"
            >取 消</el-button
          >
        </span>
      </div>
    </el-dialog>
    <div
      v-for="(item, index) in activityList"
      :key="index"
      class="modern-activity-card"
      @click="navigateTo(item)"
    >
      <!-- 活动图片区域 -->
      <div class="activity-image-section">
        <div class="image-container">
          <el-image
            v-if="getImageSrc(item)"
            class="activity-image"
            :src="getImageSrc(item)"
            fit="cover"
            :preview-src-list="getImageSrc(item) ? [getImageSrc(item)] : []"
            :z-index="9999"
            lazy
          >
            <div slot="placeholder" class="image-placeholder">
              <i class="el-icon-picture-outline"></i>
              <span>加载中...</span>
            </div>
            <div slot="error" class="image-error">
              <i class="el-icon-picture-outline"></i>
              <span>加载失败</span>
            </div>
          </el-image>

          <!-- 无图片时的占位符 -->
          <div v-else class="no-image-placeholder">
            <i class="el-icon-picture-outline"></i>
            <span>暂无图片</span>
          </div>
          <div class="image-overlay" @click.stop="previewImage(item)">
            <i class="el-icon-view"></i>
            <span>查看大图</span>
          </div>
        </div>
      </div>

      <!-- 活动信息区域 -->
      <div class="activity-info-section">
        <div class="activity-header">
          <div class="title-section">
            <h3 class="activity-title">{{ item.actName }}</h3>
            <!-- 活动类型标签 -->
            <span
              v-if="item.actType !== undefined"
              class="activity-type-tag"
              :style="getTypeStyle(item)"
              >
              {{ getActTypeLabel(item) }}
            </span>
          </div>
          <div class="activity-status">
            <span v-if="item.deleted === 2" class="status-badge status-cancelled">
              <i class="el-icon-close"></i>
              活动作废
            </span>
            <template v-else>
              <span v-if="item.status === 1" class="status-badge status-not-start">
                <i class="el-icon-time"></i>
                未开始
              </span>
              <span v-if="item.status === 2" class="status-badge status-signup">
                <i class="el-icon-user"></i>
                报名中
              </span>
              <span v-if="item.status === 3" class="status-badge status-signup-end">
                <i class="el-icon-warning"></i>
                报名截止
              </span>
              <span v-if="item.status === 4" class="status-badge status-ongoing">
                <i class="el-icon-video-play"></i>
                进行中
              </span>
              <span v-if="item.status === 5" class="status-badge status-finished">
                <i class="el-icon-circle-check"></i>
                已结束
              </span>
            </template>
          </div>
        </div>

        <div class="activity-details">
          <div class="detail-item">
            <div class="detail-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="detail-content">
              <span class="detail-label">报名时间</span>
              <span class="detail-value">{{ item.enlistStartTime }} - {{ item.enlistEndTime }}</span>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-icon">
              <i class="el-icon-location"></i>
            </div>
            <div class="detail-content">
              <span class="detail-label">活动地址</span>
              <span class="detail-value">{{ item.address }}</span>
            </div>
          </div>

          <div class="detail-item">
            <div class="detail-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="detail-content">
              <span class="detail-label">报名情况</span>
              <span class="detail-value">
                <span class="participation-count">{{ item.participationNum }}</span>
                <span class="separator">/</span>
                <span class="total-count">{{ item.actNum }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作区域 -->
      <div class="activity-actions">
        <div class="action-buttons" @click.stop>
          <el-button
            v-if="item.status === 2"
            type="primary"
            size="small"
            icon="el-icon-qrcode"
            class="qr-button"
            @click="press(item)"
          >
            生成二维码
          </el-button>
          <el-button
            type="text"
            size="small"
            icon="el-icon-view"
            class="view-button"
            @click="navigateTo(item)"
          >
            查看详情
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import VueQr from 'vue-qr';
import { getAllActType } from '@/api/Type';
import stringToColor from 'string-to-color';


export default {
  props: {
    activityList: {
      type: Array
    }
  },
  data() {
    return {
      Url: '',
      qrCodeConfig: {
        dotScale: 0.9,
        colorDark: '#663300'
      },
      AllActType: [],
      dialogVisible: false
    };
;  },
  async created() {
    try {
      const response = await getAllActType(); // 等待接口返回
      // 修复2：提取实际数据（根据接口返回结构，数据在 response.data.data 中）
      this.AllActType = response.data.data;
      // 修复3：打印时加 this
      console.log(this.AllActType);

    } catch (error) {
      console.error("获取活动类型失败：", error);
    }
  },
  methods: {
    // 获取图片源地址
    getImageSrc(item) {
      // 尝试不同的图片字段名
      let imageField = '';

      // 按优先级尝试不同字段
      if (item.actImg) {
        imageField = item.actImg;
      } else if (item.activityImage) {
        imageField = item.activityImage;
      } else if (item.image) {
        imageField = item.image;
      } else if (item.img) {
        imageField = item.img;
      }

      // 调试信息
      console.log('活动图片字段检查:', {
        actName: item.actName,
        actImg: item.actImg,
        activityImage: item.activityImage,
        image: item.image,
        img: item.img,
        selected: imageField
      });

      if (!imageField) {
        console.warn('活动图片字段为空:', item);
        return ''; // 返回空字符串，让 el-image 显示错误状态
      }

      return `/image/${imageField}`;
    },

    // 通过 actType（对应 sort 的值）获取对应的 label
    getActTypeLabel(item) {
      // 遍历 actTypeList，找到 sort 等于 actType 的项
      const matchedItem = this.AllActType.find(type => type.sort === item.actType);
      // 未找到时返回空字符串或默认值
      return matchedItem ? matchedItem.label : "";
    },

  // 获取活动类型样式（基于string-to-color）
  getTypeStyle(item) {
    if (item.actType === undefined) return {};

    // 获取活动类型标签文本
    const typeLabel = this.getActTypeLabel(item);
    if (!typeLabel) return {};

    // 使用string-to-color生成颜色
    const baseColor = stringToColor(typeLabel);

    return {
      color: baseColor,
      backgroundColor: `${baseColor}20`, // 20% 透明度背景
      borderColor: baseColor,
      border: `1px solid ${baseColor}`
    };
  },
    navigateTo(item) {
      this.$router.push({ path: '/Home/list/detail', query: { id: item.id } });
    },
    handleClose(done) {
      this.$confirm('确认关闭？')
        .then((_) => {
          done();
        })
        .catch((_) => {});
    },
    press(item) {
      this.dialogVisible = true;
      this.Url = `${process.env.VUE_APP_BASE_URL}/#/activityDetail/?actId=${item.id}`;
    },
    downloadImg() {
      const iconUrl = document.getElementsByClassName('img')[0].firstChild.src;
      const a = document.createElement('a');
      const event = new MouseEvent('click');
      a.download = '二维码';
      a.href = iconUrl;
      a.dispatchEvent(event);
    },

    // 预览图片
    previewImage(item) {
      const imageSrc = this.getImageSrc(item);
      if (!imageSrc) {
        this.$message.warning('暂无图片可预览');
        return;
      }

      // 使用 Element UI 的图片预览功能
      // 创建一个临时的图片预览
      const images = [imageSrc];

      // 手动创建预览
      import('element-ui').then(ElementUI => {
        if (ElementUI.ImageViewer) {
          const viewer = new ElementUI.ImageViewer({
            urlList: images,
            zIndex: 9999
          });
          viewer.show();
        } else {
          // 备用方案：打开新窗口显示图片
          window.open(imageSrc, '_blank');
        }
      }).catch(() => {
        // 如果导入失败，使用备用方案
        window.open(imageSrc, '_blank');
      });
    }
  },
  components: {
    VueQr
  }
};
</script>

<style lang="scss" scoped>
.activity-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  padding: 0;
}

// 现代化活动卡片
.modern-activity-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);

    .image-overlay {
      opacity: 1;
    }

    .activity-title {
      color: #409EFF;
    }
  }

  // 活动图片区域
  .activity-image-section {
    height: 180px; // 增加高度
    min-height: 180px; // 设置最小高度
    position: relative;
    overflow: hidden;

    .image-container {
      width: 100%;
      height: 100%;
      position: relative;

      .activity-image {
        width: 100%;
        height: 100%;
        min-height: 180px; // 确保最小高度

        ::v-deep img {
          width: 100%;
          height: 100%;
          min-height: 180px; // 图片最小高度
          object-fit: cover; // 保持比例并填充容器
          object-position: center; // 居中显示
          transition: transform 0.3s ease;
        }
      }

      .image-placeholder,
      .image-error {
        width: 100%;
        height: 100%;
        min-height: 180px; // 确保最小高度
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
        color: #909399;
        text-align: center;
        padding: 16px;

        i {
          font-size: 32px;
          margin-bottom: 12px;
          opacity: 0.6;
        }

        span {
          font-size: 14px;
          opacity: 0.8;
          font-weight: 500;
        }
      }

      .image-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        cursor: pointer;

        i {
          color: white;
          font-size: 24px;
          margin-bottom: 4px;
        }

        span {
          color: white;
          font-size: 12px;
          font-weight: 500;
        }
      }
    }
  }

  // 活动信息区域
  .activity-info-section {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;

    .activity-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 16px;

      .title-section {
        flex: 1;
        margin-right: 12px;

        .activity-title {
          font-size: 18px;
          font-weight: 700;
          color: #2c3e50;
          margin: 0 0 8px 0;
          line-height: 1.3;
          transition: color 0.3s ease;

          // 文本溢出处理
          display: -webkit-box;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .activity-type-tag {
          display: inline-flex;
          align-items: center;
          padding: 4px 10px;
          border-radius: 12px;
          font-size: 11px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

          &.type-hiking {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;

            &::before {
              content: '🥾';
              margin-right: 4px;
            }
          }

          &.type-custom {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;

            &::before {
              content: '🎯';
              margin-right: 4px;
            }
          }
        }
      }

      .activity-status {
        flex-shrink: 0;
      }
    }

    .activity-details {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 12px;

      .detail-item {
        display: flex;
        align-items: center;
        gap: 12px;

        .detail-icon {
          width: 32px;
          height: 32px;
          border-radius: 8px;
          background: linear-gradient(135deg, #409EFF, #36a3f7);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 14px;
          flex-shrink: 0;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

          // 确保图标完全居中
          i {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            line-height: 1;
            margin: 0;
            padding: 0;
          }
        }

        .detail-content {
          flex: 1;
          min-width: 0;

          .detail-label {
            display: block;
            font-size: 12px;
            color: #909399;
            margin-bottom: 2px;
            font-weight: 500;
          }

          .detail-value {
            display: block;
            font-size: 14px;
            color: #2c3e50;
            font-weight: 600;
            line-height: 1.4;

            // 文本溢出处理
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;

            .participation-count {
              color: #67C23A;
              font-weight: 700;
            }

            .separator {
              margin: 0 4px;
              color: #909399;
            }

            .total-count {
              color: #606266;
            }
          }
        }
      }
    }
  }

  // 操作区域
  .activity-actions {
    padding: 16px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    background: rgba(248, 249, 250, 0.8);

    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: flex-end;

      .qr-button {
        background: linear-gradient(135deg, #67C23A, #5daf34);
        border: none;
        color: white;
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
        }
      }

      .view-button {
        color: #409EFF;
        border: 1px solid #409EFF;
        border-radius: 8px;
        padding: 8px 16px;
        font-size: 12px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          background: #409EFF;
          color: white;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }
}

// 状态徽章样式
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  i {
    font-size: 12px;
  }

  &.status-not-start {
    background: linear-gradient(135deg, #909399, #787c82);
    color: white;
  }

  &.status-signup {
    background: linear-gradient(135deg, #67C23A, #5daf34);
    color: white;
    animation: pulse 2s infinite;
  }

  &.status-signup-end {
    background: linear-gradient(135deg, #E6A23C, #cf9236);
    color: white;
  }

  &.status-ongoing {
    background: linear-gradient(135deg, #F56C6C, #f04747);
    color: white;
    animation: pulse 2s infinite;
  }

  &.status-finished {
    background: linear-gradient(135deg, #909399, #787c82);
    color: white;
  }

  &.status-cancelled {
    background: linear-gradient(135deg, #F56C6C, #f04747);
    color: white;
  }
}

// 二维码对话框样式
.qr-dialog {
  .el-dialog {
    border-radius: 12px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #409EFF, #36a3f7);
    color: white;
    padding: 16px 20px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: white;
        font-size: 18px;

        &:hover {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    text-align: center;
    background: #f8f9fa;
  }
}

.img {
  margin-left: -20px;
}

.qr-code-download {
  margin-bottom: 20px;
  margin-left: 15px;

  .img {
    background: white;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    display: inline-block;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .activity-list {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .activity-list {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 12px; // 移动端添加左右内边距
  }

  .modern-activity-card {
    height: auto;
    min-height: 280px;
    border-radius: 12px; // 移动端减小圆角
    margin-bottom: 8px; // 增加底部间距

    &:hover {
      transform: translateY(-4px); // 移动端减少悬停效果
    }

    .activity-image-section {
      height: 140px; // 增加移动端图片高度
      min-height: 140px;

      .image-container {
        .activity-image {
          min-height: 140px;

          ::v-deep img {
            min-height: 140px;
          }
        }

        .image-placeholder,
        .image-error {
          min-height: 140px;
          padding: 12px;

          i {
            font-size: 28px;
            margin-bottom: 8px;
          }

          span {
            font-size: 13px;
          }
        }

        .image-overlay {
          i {
            font-size: 20px;
          }

          span {
            font-size: 11px;
          }
        }
      }
    }

    .activity-info-section {
      padding: 16px;

      .activity-header {
        margin-bottom: 14px;
        flex-direction: column; // 移动端垂直布局
        align-items: flex-start;
        gap: 8px;

        .title-section {
          margin-right: 0;
          width: 100%;

          .activity-title {
            font-size: 16px; // 移动端减小标题字体
            line-height: 1.4;
            margin-bottom: 6px;
          }

          .activity-type-tag {
            font-size: 10px;
            padding: 3px 8px;
            border-radius: 10px;
          }
        }

        .activity-status {
          align-self: flex-end; // 状态标签右对齐
        }
      }

      .activity-details {
        gap: 10px;

        .detail-item {
          gap: 10px;

          .detail-icon {
            width: 28px;
            height: 28px;
            font-size: 12px;
            border-radius: 6px;
          }

          .detail-content {
            .detail-label {
              font-size: 11px;
              margin-bottom: 1px;
            }

            .detail-value {
              font-size: 13px;
              line-height: 1.3;

              // 移动端允许换行显示长文本
              white-space: normal;
              overflow: visible;
              text-overflow: unset;

              // 限制最大行数
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }
        }
      }
    }

    .activity-actions {
      padding: 12px 16px;

      .action-buttons {
        gap: 6px;
        flex-wrap: wrap; // 移动端允许按钮换行

        .qr-button,
        .view-button {
          padding: 6px 12px;
          font-size: 11px;
          border-radius: 6px;
          flex: 1; // 移动端按钮等宽
          min-width: 80px;
          text-align: center;

          &:hover {
            transform: translateY(-1px); // 移动端减少悬停效果
          }
        }
      }
    }
  }

  // 状态徽章移动端优化
  .status-badge {
    padding: 4px 8px;
    font-size: 10px;
    border-radius: 12px;

    i {
      font-size: 10px;
    }
  }

  // 二维码弹窗移动端适配
  .qr-dialog {
    .el-dialog {
      width: 90% !important;
      margin: 0 auto;
      border-radius: 12px;
    }

    .el-dialog__header {
      padding: 12px 16px;

      .el-dialog__title {
        font-size: 16px;
      }
    }

    .el-dialog__body {
      padding: 16px;
    }

    .qr-code-download {
      margin-bottom: 16px;
      margin-left: 0;

      .img {
        padding: 12px;

        canvas {
          max-width: 100%;
          height: auto;
        }
      }
    }

    .dialog-footer {
      .el-button {
        padding: 8px 16px;
        font-size: 14px;
        margin: 0 4px;
      }
    }
  }
}

// 小屏手机适配 (480px以下)
@media (max-width: 480px) {
  .activity-list {
    padding: 0 8px; // 更小的内边距
    gap: 12px;
  }

  .modern-activity-card {
    border-radius: 10px;

    .activity-image-section {
      height: 120px;
      min-height: 120px;

      .image-container {
        .activity-image {
          min-height: 120px;

          ::v-deep img {
            min-height: 120px;
          }
        }

        .image-placeholder,
        .image-error {
          min-height: 120px;
          padding: 8px;

          i {
            font-size: 24px;
            margin-bottom: 6px;
          }

          span {
            font-size: 12px;
          }
        }
      }
    }

    .activity-info-section {
      padding: 12px;

      .activity-header {
        .title-section {
          .activity-title {
            font-size: 15px;
            -webkit-line-clamp: 1; // 小屏只显示一行标题
            line-clamp: 1;
          }

          .activity-type-tag {
            font-size: 9px;
            padding: 2px 6px;
          }
        }
      }

      .activity-details {
        gap: 8px;

        .detail-item {
          gap: 8px;

          .detail-icon {
            width: 24px;
            height: 24px;
            font-size: 11px;
          }

          .detail-content {
            .detail-label {
              font-size: 10px;
            }

            .detail-value {
              font-size: 12px;
              -webkit-line-clamp: 1; // 小屏只显示一行
              line-clamp: 1;
            }
          }
        }
      }
    }

    .activity-actions {
      padding: 10px 12px;

      .action-buttons {
        .qr-button,
        .view-button {
          padding: 5px 10px;
          font-size: 10px;
          min-width: 70px;
        }
      }
    }
  }

  // 状态徽章小屏优化
  .status-badge {
    padding: 3px 6px;
    font-size: 9px;

    i {
      font-size: 9px;
    }
  }

  // 二维码弹窗小屏适配
  .qr-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }

    .el-dialog__header {
      padding: 10px 12px;

      .el-dialog__title {
        font-size: 14px;
      }
    }

    .el-dialog__body {
      padding: 12px;
    }

    .qr-code-download {
      margin-bottom: 12px;

      .img {
        padding: 8px;

        canvas {
          width: 160px !important;
          height: 160px !important;
        }
      }
    }

    .dialog-footer {
      .el-button {
        padding: 6px 12px;
        font-size: 12px;
        margin: 0 2px;
      }
    }
  }
}
</style>
