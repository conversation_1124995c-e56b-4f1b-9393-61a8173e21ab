// ===== 通用组件样式 =====

// === 按钮组件 ===
.btn {
  @include button-reset;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-base;
  font-size: $font-size-base;
  font-weight: 500;
  line-height: $line-height-base;
  border-radius: $border-radius-base;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 按钮尺寸
  &--sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
  
  &--lg {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-lg;
  }
  
  // 现代化按钮类型
  &--primary {
    background: linear-gradient(135deg, $color-primary 0%, $color-primary-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-primary, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-primary-light 0%, $color-primary 100%);
      box-shadow: 0 6px 12px rgba($color-primary, 0.3);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba($color-primary, 0.2);
    }
  }

  &--secondary {
    background: linear-gradient(135deg, $color-secondary 0%, $color-secondary-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-secondary, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-secondary-light 0%, $color-secondary 100%);
      box-shadow: 0 6px 12px rgba($color-secondary, 0.3);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba($color-secondary, 0.2);
    }
  }

  &--tertiary {
    background: linear-gradient(135deg, $color-tertiary 0%, $color-tertiary-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-tertiary, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-tertiary-light 0%, $color-tertiary 100%);
      box-shadow: 0 6px 12px rgba($color-tertiary, 0.3);
      transform: translateY(-2px);
    }
  }

  &--outline {
    background-color: transparent;
    color: $color-primary;
    border: 2px solid $color-primary;
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-primary 0%, $color-primary-dark 100%);
      color: white;
      box-shadow: 0 4px 8px rgba($color-primary, 0.2);
      transform: translateY(-1px);
    }
  }

  &--ghost {
    background-color: transparent;
    color: $color-text-primary;
    border-radius: 10px;

    &:hover:not(:disabled) {
      background-color: $color-bg-tertiary;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  &--success {
    background: linear-gradient(135deg, $color-success 0%, $color-success-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-success, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-success-light 0%, $color-success 100%);
      box-shadow: 0 6px 12px rgba($color-success, 0.3);
      transform: translateY(-2px);
    }
  }

  &--warning {
    background: linear-gradient(135deg, $color-warning 0%, $color-warning-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-warning, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-warning-light 0%, $color-warning 100%);
      box-shadow: 0 6px 12px rgba($color-warning, 0.3);
      transform: translateY(-2px);
    }
  }

  &--error {
    background: linear-gradient(135deg, $color-error 0%, $color-error-dark 100%);
    color: white;
    box-shadow: 0 4px 6px rgba($color-error, 0.2);
    border-radius: 10px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, $color-error-light 0%, $color-error 100%);
      box-shadow: 0 6px 12px rgba($color-error, 0.3);
      transform: translateY(-2px);
    }
  }
}

// === 卡片组件 ===
.card {
  @include card;
  
  &__header {
    padding-bottom: $spacing-base;
    border-bottom: 1px solid $color-border-secondary;
    margin-bottom: $spacing-base;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
      padding-bottom: 0;
    }
  }
  
  &__title {
    font-size: $font-size-lg;
    font-weight: 600;
    color: $color-text-primary;
    margin: 0;
  }
  
  &__body {
    color: $color-text-secondary;
    line-height: $line-height-base;
  }
  
  &__footer {
    padding-top: $spacing-base;
    border-top: 1px solid $color-border-secondary;
    margin-top: $spacing-base;
    
    &:first-child {
      border-top: none;
      margin-top: 0;
      padding-top: 0;
    }
  }
  
  // 卡片变体
  &--bordered {
    border: 1px solid $color-border-primary;
  }
  
  &--hoverable {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: $shadow-lg;
      transform: translateY(-2px);
    }
  }
}

// === 输入框组件 ===
.input {
  @include input-base;
  width: 100%;
  
  &--sm {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
  
  &--lg {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-lg;
  }
  
  &--error {
    border-color: $color-error;
    
    &:focus {
      border-color: $color-error;
      box-shadow: 0 0 0 2px rgba($color-error, 0.2);
    }
  }
}

// === 标签组件 ===
.tag {
  display: inline-flex;
  align-items: center;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-sm;
  font-weight: 500;
  border-radius: $border-radius-sm;
  background-color: $color-bg-tertiary;
  color: $color-text-secondary;
  
  &--primary {
    background-color: rgba($color-primary, 0.1);
    color: $color-primary;
  }
  
  &--success {
    background-color: rgba($color-success, 0.1);
    color: $color-success;
  }
  
  &--warning {
    background-color: rgba($color-warning, 0.1);
    color: $color-warning;
  }
  
  &--error {
    background-color: rgba($color-error, 0.1);
    color: $color-error;
  }
}

// === 徽章组件 ===
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  padding: 0 6px;
  font-size: $font-size-xs;
  font-weight: 500;
  line-height: 1;
  color: white;
  background-color: $color-error;
  border-radius: 10px;
  
  &--dot {
    width: 8px;
    height: 8px;
    min-width: 8px;
    padding: 0;
    border-radius: 50%;
  }
}

// === 分割线组件 ===
.divider {
  border: none;
  border-top: 1px solid $color-border-secondary;
  margin: $spacing-base 0;
  
  &--vertical {
    display: inline-block;
    width: 1px;
    height: 1em;
    border-top: none;
    border-left: 1px solid $color-border-secondary;
    margin: 0 $spacing-sm;
  }
}

// === 加载器组件 ===
.loader {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid $color-border-secondary;
  border-top-color: $color-primary;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  
  &--sm {
    width: 16px;
    height: 16px;
  }
  
  &--lg {
    width: 24px;
    height: 24px;
  }
}

// === 头像组件 ===
.avatar {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: $color-bg-tertiary;
  color: $color-text-secondary;
  font-weight: 500;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  &--sm {
    width: 32px;
    height: 32px;
    font-size: $font-size-sm;
  }
  
  &--lg {
    width: 48px;
    height: 48px;
    font-size: $font-size-lg;
  }
  
  &--square {
    border-radius: $border-radius-base;
  }
}

// === 面包屑组件 ===
.breadcrumb {
  display: flex;
  align-items: center;
  font-size: $font-size-sm;
  color: $color-text-secondary;
  
  &__item {
    display: flex;
    align-items: center;
    
    &:not(:last-child)::after {
      content: '/';
      margin: 0 $spacing-sm;
      color: $color-text-tertiary;
    }
    
    a {
      color: $color-text-secondary;
      text-decoration: none;
      
      &:hover {
        color: $color-primary;
      }
    }
    
    &--active {
      color: $color-text-primary;
    }
  }
}
