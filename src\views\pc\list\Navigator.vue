<template>
  <div class="modern-navigator">
    <div class="nav-header">
      <h3 class="nav-title">活动筛选</h3>
      <p class="nav-subtitle">选择要查看的活动类型</p>
    </div>

    <div class="nav-tabs">
      <div
        v-for="item in navItems"
        :key="item.index"
        class="nav-tab"
        :class="{ active: activeIndex === item.index }"
        @click="handleSelect(item.index)"
      >
        <div class="tab-icon">
          <i :class="item.icon"></i>
        </div>
        <div class="tab-content">
          <span class="tab-label">{{ item.label }}</span>
          <span class="tab-description">{{ item.description }}</span>
        </div>
        <div class="tab-indicator"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernNavigator',
  props: {
    activeIndex: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      navItems: [
        {
          index: '1',
          label: '报名中',
          description: '正在接受报名的活动',
          icon: 'el-icon-user-solid'
        },
        {
          index: '3',
          label: '未开始',
          description: '即将开始的活动',
          icon: 'el-icon-time'
        },
        {
          index: '5',
          label: '全部',
          description: '查看所有活动',
          icon: 'el-icon-menu'
        }
      ]
    }
  },
  methods: {
    handleSelect(key) {
      if (key !== this.activeIndex) {
        this.$emit('handleSelect', key);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.modern-navigator {
  // 导航头部
  .nav-header {
    margin-bottom: 20px;

    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 4px 0;
    }

    .nav-subtitle {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }
  }

  // 导航标签
  .nav-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;

    .nav-tab {
      position: relative;
      display: flex;
      align-items: center;
      padding: 16px 20px;
      background: #fff;
      border: 2px solid #ebeef5;
      border-radius: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        border-color: rgba($color-primary, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba($color-primary, 0.1);
      }

      &.active {
        border-color: $color-primary;
        background: linear-gradient(135deg, rgba($color-primary, 0.05), rgba($color-primary, 0.1));

        .tab-icon {
          background: $color-primary;
          color: #fff;
          transform: scale(1.1);
        }

        .tab-content {
          .tab-label {
            color: $color-primary;
            font-weight: 600;
          }
        }

        .tab-indicator {
          transform: scaleX(1);
        }
      }

      .tab-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: rgba($color-primary, 0.1);
        color: $color-primary;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-right: 16px;
        transition: all 0.3s ease;
        flex-shrink: 0;
      }

      .tab-content {
        flex: 1;

        .tab-label {
          display: block;
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 4px;
          transition: all 0.3s ease;
        }

        .tab-description {
          font-size: 12px;
          color: #909399;
          line-height: 1.4;
        }
      }

      .tab-indicator {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(135deg, $color-primary, $color-primary-light);
        transform: scaleX(0);
        transition: transform 0.3s ease;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .modern-navigator {
    .nav-tabs {
      grid-template-columns: 1fr;
      gap: 12px;

      .nav-tab {
        padding: 12px 16px;

        .tab-icon {
          width: 36px;
          height: 36px;
          font-size: 16px;
          margin-right: 12px;
        }

        .tab-content {
          .tab-label {
            font-size: 14px;
          }

          .tab-description {
            font-size: 11px;
          }
        }
      }
    }
  }
}

@media screen and (max-width: 480px) {
  .modern-navigator {
    .nav-header {
      margin-bottom: 16px;

      .nav-title {
        font-size: 16px;
      }

      .nav-subtitle {
        font-size: 12px;
      }
    }

    .nav-tabs {
      .nav-tab {
        padding: 10px 12px;

        .tab-icon {
          width: 32px;
          height: 32px;
          font-size: 14px;
          margin-right: 10px;
        }

        .tab-content {
          .tab-label {
            font-size: 13px;
          }

          .tab-description {
            font-size: 10px;
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-tab {
  animation: slideIn 0.6s ease-out;
  animation-fill-mode: both;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}
</style>
