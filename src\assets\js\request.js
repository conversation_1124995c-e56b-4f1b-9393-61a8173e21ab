import axios from 'axios';
axios.defaults.withCredentials = true;
// 定义cancel队列，用于路由跳转时取消pending状态的请求
export const cancelTokenSources = new Map();
let from = 'pc';
let isConfirm = false;
const myIntercepter = axios.interceptors.request.use(
  function(config) {
    // 防止重复点击请求
    window.loading = true;
    // 排除不需要cancel的请求
    /* eslint-disable-next-line */
    if (config.params && config.params.hasOwnProperty('cancelToken')) {
      return config;
    }
    // 判断请求是从哪个接口发出的,判断一次
    if (!isConfirm && config.url.indexOf('mobile') > -1) {
      from = 'mobile';
    }
    const source = axios.CancelToken.source();

    // 加入cancel队列
    cancelTokenSources.set(source.token, source.cancel);
    config.cancelToken = source.token;
    return config;
  },
  function(error) {
    return Promise.reject(error);
  }
);

let getCurrentUrl = '';
// 记录#字符的位置
let speStrIndex = 0;
axios.interceptors.response.use(
  (response) => {
    window.loading = false;

    // 如果已经接收到相应了则移除cancel队列
    response.config.cancelToken && cancelTokenSources.delete(response.config.cancelToken);
    // 登陆成功后直接返回response

    if ((response.data && response.data.status === 2) || response.data.status !== -2) {
      isConfirm = true;
      return response;
    // 未登陆走微信登陆逻辑
    } else if (response.data && response.data.status === -2) {
      getCurrentUrl = window.location.href;
      // 获取企业微信跳转参数
      const header = response.data.data;
      // 获取#字符的位置，先将该字符切掉，记录位置后作为参数传入
      speStrIndex = getCurrentUrl.indexOf('#');
      getCurrentUrl = `${getCurrentUrl.substring(0, speStrIndex) + getCurrentUrl.substring(speStrIndex + 1, getCurrentUrl.length)}slicefrom=${speStrIndex}`;
      // 传入的参数会自动过滤掉#字符以及后边的所有字符;
      let loginUrl = '';
      if (from === 'mobile') {
        loginUrl = `${header}&response_type=code&scope=snsapi_base&redirect_uri=http://moa.unionman.net/auth.html?lastUrl=${`--${getCurrentUrl}`}&state=mobile#wechat_redirect`;
        window.location.href = loginUrl;
      } else {
        const nextUrl = window.location.origin;
        loginUrl = `${nextUrl}/admin#/Login`;
        window.location.href = loginUrl;
      }
      // const loginUrl = from === 'mobile'
      //   ? `${header}&response_type=code&scope=snsapi_base&redirect_uri=http://moa.unionman.net/auth.html?lastUrl=${`--${getCurrentUrl}`}&state=mobile#wechat_redirect`
      //   : `${header}&redirect_uri=http://moa.unionman.net/auth.html?lasturl=${`--${getCurrentUrl}`}&state=pc`;
      // window.location.href = loginUrl;
    }
  },
  (error) => {
    window.loading = false;

    axios.isCancel(error) && cancelTokenSources.delete(error.message);
    return error.response;
  }
);
const axiosWithoutToken = axios.create();
axiosWithoutToken.interceptors.request.eject(myIntercepter);

export const axiosRequest = axios;

export const postRequest = (url, params) => {
  return axios({
    method: 'post',
    url: `${url}`,
    data: params,
    transformRequest: [
      function(data) {
        let ret = '';
        for (const it in data) {
          ret += `${encodeURIComponent(it)}=${encodeURIComponent(data[it])}&`;
        }
        return ret;
      }
    ],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  });
};

export const postBodyRequest = (url, params) => {
  return axios({
    method: 'post',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

export const postBodyRequestWithoutCredentials = (url, params) => {
  return axiosWithoutToken({
    method: 'post',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

export const putBodyRequest = (url, params) => {
  return axios({
    method: 'put',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

export const putRequest = url => {
  return axios({
    method: 'put',
    url: `${url}`
  });
};

export const deleteBodyRequest = (url, params) => {
  return axios({
    method: 'delete',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

export const deleteRequest = url => {
  return axios({
    method: 'delete',
    url: `${url}`
  });
};

export const getRequest = (url, params) => {
  return axios({
    method: 'get',
    url: `${url}`,
    params
  });
};

export const patchBodyRequest = (url, params) => {
  return axios({
    method: 'patch',
    url: `${url}`,
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=utf-8'
    }
  });
};

export const getTitleRequest = url => {
  return axiosWithoutToken({
    method: 'get',
    url: `${url}`
  });
};

export const exportFile = (url, params) => {
  return axios({
    method: 'post',
    url: `${url}`,
    data: params,
    responseType: 'blob'
  });
};

export const download = (url, params, onDownloadProgress) => {
  return axios({
    method: 'get',
    url,
    params,
    responseType: 'blob',
    onDownloadProgress
  });
};

export const uploadFile = (url, data) => {
  return axios({
    method: 'post',
    url,
    data,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};
