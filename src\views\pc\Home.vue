<template>
  <div class="home">
    <!-- 新的头部导航 -->
    <div class="modern-header">
      <!-- 左上角图标和标题 -->
      <div class="header-left">
        <div class="logo-section">
          <div class="logo-icon">
             <img src="../../assets/images/logo.png" alt="品牌图标" class="custom-icon">
          </div>
        </div>
      </div>

      <!-- 中间导航菜单 -->
      <div class="header-center">
        <nav class="nav-menu">
          <router-link
            to="/Home/list"
            class="nav-item"
            :class="{ active: $route.path === '/Home/list' }"
          >
            <i class="el-icon-document"></i>
            <span>活动列表</span>
          </router-link>
          <router-link
            to="/Home/public"
            class="nav-item"
            :class="{ active: $route.path === '/Home/public' }"
          >
            <i class="el-icon-plus"></i>
            <span>创建发布</span>
          </router-link>
          <router-link
            to="/Home/type"
            class="nav-item"
            :class="{ active: $route.path === '/Home/type' }"
          >
            <i class="el-icon-setting"></i>
            <span>活动类型管理</span>
          </router-link>
        </nav>
      </div>

      <!-- 右上角用户信息 -->
      <div class="header-right">
        <user-dropdown />
      </div>
    </div>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-wrapper" :style="{height: autoHeight}">
        <transition name="fade-transform" mode="out-in">
          <router-view :key="nowPath" />
        </transition>
      </div>
    </main>
  </div>
</template>

<script>
import UserDropdown from '@/components/UserDropdown.vue';

export default {
  name: 'Home',
  components: {
    UserDropdown
  },
  created() {
    // 默认跳转到活动列表页面
    if (this.$route.path === '/Home' || this.$route.path === '/Home/') {
      this.$router.replace('/Home/list');
    }
  },
  computed: {
    // 当前的路径
    nowPath() {
      return this.$route.path;
    },
    // 自动高度,高度是对于页面高度减去头部高度
    autoHeight() {
      // 根据屏幕尺寸动态计算高度
      if (window.innerWidth <= 480) {
        return `calc(100vh - 50px)`; // 小屏手机
      } else if (window.innerWidth <= 768) {
        return `calc(100vh - 60px)`; // 平板
      } else {
        return `calc(100vh - 80px)`; // PC端
      }
    }
  },
  mounted() {
    // const token = sessionStorage.getItem('token');
    // this.$store.commit('setAuthorization', token);
  }
};
</script>
<style lang="scss" scoped>
@import "~@/assets/css/variables";

.home {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
}

.modern-header {
  height: 80px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  position: relative;
  z-index: 100;

  // 移动端适配 - 减少高度和内边距
  @media screen and (max-width: 768px) {
    height: 60px;
    padding: 0 16px;
  }

  @media screen and (max-width: 480px) {
    height: 50px;
    padding: 0 12px;
  }

  .header-left {
    // 移动端隐藏左侧logo区域以节省空间
    @media screen and (max-width: 768px) {
      display: none;
    }

    .logo-section {
      display: flex;
      align-items: center;
      color: #fff;

      .logo-icon {
        width: 128px;
        height: 64px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        i {
          font-size: 24px;
          color: #fff;
        }
      }

      .logo-text {
        h1 {
          font-size: 24px;
          font-weight: 700;
          margin: 0;
          line-height: 1.2;
          background: linear-gradient(135deg, #fff 0%, rgba(255, 255, 255, 0.8) 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        p {
          font-size: 12px;
          margin: 0;
          opacity: 0.8;
          font-weight: 300;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }

  .header-center {
    // 移动端导航菜单优化
    @media screen and (max-width: 768px) {
      flex: 1;
      margin: 0 16px;
    }

    @media screen and (max-width: 480px) {
      margin: 0 8px;
    }

    .nav-menu {
      display: flex;
      gap: 8px;

      // 移动端导航菜单间距优化
      @media screen and (max-width: 768px) {
        gap: 6px;
        justify-content: center;
      }

      @media screen and (max-width: 480px) {
        gap: 4px;
      }

      .nav-item {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        border-radius: 10px;
        text-decoration: none;
        color: rgba(255, 255, 255, 0.8);
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);

        // 移动端导航项优化
        @media screen and (max-width: 768px) {
          padding: 10px 16px;
          font-size: 13px;
        }

        @media screen and (max-width: 480px) {
          padding: 8px 12px;
          font-size: 12px;

          span {
            display: none; // 小屏幕隐藏文字，只显示图标
          }
        }

        i {
          margin-right: 8px;
          font-size: 16px;

          // 移动端图标优化
          @media screen and (max-width: 768px) {
            font-size: 14px;
            margin-right: 6px;
          }

          @media screen and (max-width: 480px) {
            margin-right: 0; // 只显示图标时不需要右边距
            font-size: 16px;
          }
        }

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          color: #fff;
          transform: translateY(-2px);
        }

        &.active {
          background: rgba(255, 255, 255, 0.25);
          color: #fff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;

    // 移动端用户区域优化
    @media screen and (max-width: 768px) {
      flex-shrink: 0;
    }
  }
}

.main-content {
  flex: 1;
  overflow: hidden;

  .content-wrapper {
    height: 100%;
    overflow-y: auto;
    background: rgba(255, 255, 255, 0.05);
  }
}

// 页面过渡动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}
</style>
