worker_processes auto; #工作进程数
#pid /usr/local/nginx/logs/nginx.pid;
#error_log /usr/local/nginx/logs/error.log error;
worker_rlimit_nofile 100000;

events {
	worker_connections 65536; #最大连接数
	multi_accept on; #多连接
	use epoll;   
}

http {
	include mime.types;
	default_type application/octet-stream;

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	server_tokens off;
  

  #客户端连接超时时间  
	keepalive_timeout 10;
	client_header_timeout 10;
	client_body_timeout 10;
	reset_timedout_connection on;
	send_timeout 10;

	limit_conn_zone $binary_remote_addr zone=addr:5m;
	limit_conn addr 100;

	gzip on;  # 开启压缩
	gzip_disable "msie6"
	gzip_static on;
	gzip_proxied any;
	gzip_min_length 1000;
	gzip_comp_level 4;
	gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript;

	open_file_cache max=100000 inactive=20s;
	open_file_cache_valid 30s;
	open_file_cache_min_uses 2;
	open_file_cache_errors on;

	# include /etc/nginx/conf.d/*.conf;
	# include /etc/nginx/sites-enabled/*;

  map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
  }

	server {
    # 接口服务的IP地址
		listen 8080;
		
    # 域名
		server_name localhost;
		charset utf-8;
		access_log off;

		root /usr/share/nginx/html;
        
		client_max_body_size 200m;

		location / {
			index index.html index.htm;
		}

   	location /back/ {
			proxy_pass http://***************:9999/;
		}
		location /api/ {
			proxy_pass http://***************:9999/;
		}
		location /image/ {
			proxy_pass http://***************:9999/;
		}
		error_page 404 /404.html;
		error_page 500 /500.html;
	}
}
