<template>
  <div class="container">
    <van-nav-bar title="" left-text="返回" left-arrow @click-left="onBack" />

    <van-loading v-if="loading" size="36px" vertical class="loading">加载中...</van-loading>

    <template v-if="!loading">
      <div class="content">
        <div class="activity-info">
          <div class="img-wrapper" v-if="activity.actImg !== ''">
            <van-image :src="'/image/'+activity.actImg" />
          </div>
          <div class="desc-container">
            <div class="title">
              <div class="name">
                <div class="activity-title-wrapper">
                  <span class="activity-title">{{ activity.actName }}</span>
                  <!-- 活动类型标签 -->
                  <span
                    v-if="activity.actType !== undefined"
                    class="activity-type-tag"
                    :style="getTypeStyle(activity.actType)"
                  >
                    {{ getTypeText(activity.actType) }}
                  </span>
                </div>
                <div class="status-tags">
                  <span
                    v-if="activity.status === 1"
                    class="activity-not-start"
                    >[未开始]</span
                  >
                  <span
                    v-if="activity.status === 2"
                    class="activity-sign-up"
                    >[报名中]</span
                  >
                  <span
                    v-if="activity.status === 3"
                    class="activity-sign-up-end"
                    >[报名截止]</span
                  >
                  <span
                    v-if="activity.status === 4"
                    class="activity-ongoing"
                    >[进行中]</span
                  >
                  <span
                    v-if="activity.status === 5"
                    class="activity-finish"
                    >[已结束]</span
                  >
                </div>
              </div>
              <div
                class="appendix"
                v-if="
                  activity.activityDetail &&
                  activity.activityDetail.enclosure !== ''
                "
              >
                <a
                  :href="appendixUrl"
                  @click="stopBubble"
                  :download="appendixUrl"
                  >下载附件</a
                >
              </div>
            </div>
            <van-divider />
            <div class="list-item">
              <span class="icon">
                <van-icon name="clock-o" />
              </span>
              <span class="text">
                报名时间:
                {{ activity.enlistStartTime + " - " + activity.enlistEndTime }}
              </span>
            </div>
            <div class="list-item">
              <span class="icon">
                <van-icon name="friends" />
              </span>
              <span class="text">
                报名人数:
                {{ activity.participationNum + " / " + activity.actNum }}
              </span>
            </div>
            <div class="list-item">
              <span class="icon">
                <van-icon name="gold-coin-o" />
              </span>
              <span class="text">
                报名费用:
                {{ activity.enlistCost }}元
              </span>
            </div>
            <div class="list-item">
              <span class="icon">
                <van-icon name="clock-o" />
              </span>
              <span class="text">
                活动时间:
                {{ activity.actStartTime + " - " + activity.actEndTime }}
              </span>
            </div>
            <div class="list-item">
              <span class="icon">
                <van-icon name="location" />
              </span>
              <span class="text"> 地址: {{ activity.address }} </span>
            </div>
            <div class="list-item">
              <span class="icon">
                <van-icon name="gift-o" />
              </span>
              <span class="text"> 奖品 </span>
            </div>
            <div class="list-item">
              <!-- <span class="icon"> </span> -->
              <span class="text" v-if="price.length !== 0">
                <my-table :thead="rewardThead" :data="price" />
              </span>
              <span class="text" style="text-align:center" v-else>
                本次活动无奖品
              </span>
            </div>
          </div>
        </div>
        <div class="activity-count" v-if="activity.activityDetail">
          <div class="signup-number">
            <span class="decorate"></span>
            <span class="signup-number-title">{{activity.activityDetail.isTeam !== 0 ? '队伍数量 / 已报名人数 / 活动总人数' : '已报名人数 / 活动总人数'}}</span>
            <span class="signup-number-detail">{{
              activity.activityDetail.isTeam !== 0 ?
              teamLength +
              " / " +
              activity.participationNum +
              " / " +
              activity.actNum
              :
              activity.participationNum +
              " / " +
              activity.actNum
            }}</span>
          </div>
          <template v-if="activity.activityDetail.isTeam !== 0">
          <van-divider />
          <my-table
            :thead="acountThead"
            :data="team"
            :isTeam="activity.activityDetail.isTeam"
            border
            @on-click-tr="onShowMember"
            showPagination
            :currentPage="currentPage"
            :totalPages="totalPages"
            :perPage="perPage"
          >
            <template v-slot:operate="{data}">
              <van-button v-if="status === 0 && activity.status === 2" type="default" class="team-btn" @click.stop="handleJoinTeam(data)">组队</van-button>
            </template>
          </my-table>
          </template>
        </div>
        <div class="activity-instructions" v-if="activity.activityContentList && activity.activityContentList.length !== 0">
          <van-tabs v-model="active" animated swipeable>
            <van-tab
              v-for="(item, index) in activity.activityContentList"
              :key="index"
              :title="item.title"
            >
              <!-- {{ item.content }} -->
              <article v-html="item.content"></article>
            </van-tab>
          </van-tabs>
        </div>
      </div>
      <van-dialog v-model="showTeam" title="队伍名单">
        <my-table :thead="teamThead" :data="teamData" border />
      </van-dialog>
      <van-goods-action class="footer">
        <van-goods-action-icon
          icon="home-o"
          text="更多活动"
          color="#2563eb"
          @click="moreActivity"
        />
        <van-goods-action-icon
          v-if="price.length !== 0"
          icon="point-gift-o"
          text="奖品详情"
          @click="onShowDetail"
          color="#2563eb"
        />
        <van-goods-action-button
          text="未开始"
          color="#2563eb"
          v-if="activity.status === 1"
        />
        <van-goods-action-button
          v-if="activity.status === 4"
          color="grey"
          disabled
          text="进行中"
        />
        <van-goods-action-button
          v-if="activity.status === 3"
          color="grey"
          disabled
          text="报名截止"
        />
        <van-goods-action-button
          v-if="activity.status === 5"
          color="grey"
          disabled
          text="活动结束"
        />
        <van-goods-action-button
          text="立即报名"
          color="#2563eb"
          @click="onSignUp"
          v-if="status === 0 && activity.status === 2"
        />
        <van-goods-action-button
          v-if="status === 3 && (activity.status === 2 || activity.status === 3)"
          type="danger"
          disabled
          text="等待组队"
        />
        <van-goods-action-button
          v-if="status === 2 && (activity.status === 2 || activity.status === 3)"
          type="danger"
          disabled
          text="审核中"
        />
        <van-goods-action-button
          v-if="status === 5 && (activity.status === 2 || activity.status === 3)"
          type="danger"
          disabled
          text="候补中"
        />
        <van-goods-action-button
          v-if="status === 1 && (activity.status === 2 || activity.status === 3)"
          color="grey"
          disabled
          text="报名成功"
        />
        <van-goods-action-button
          v-if="status === 6 && (activity.status === 2 || activity.status === 3)"
          color="grey"
          disabled
          text="活动人数已满"
        />
        <van-goods-action-button
          v-if="(status === 1 || status === 2 || status === 3 || status === 5|| status ===6) && (activity.status === 2 || activity.status === 3)"
          color="red"
          @click="cancel"
          text="取消报名"
        />
        <van-goods-action-button
          v-if="status === 4 && (activity.status === 2 || activity.status === 3)"
          type="danger"
          disabled
          text="报名失败"
        />
      </van-goods-action>
    </template>
  </div>
</template>

<script>
import MyTable from '@/components/Table.vue';
import { getActivityDetailFromMobile } from '../../../api/activity/index';
import requestMixin from '../../../mixin/requestMixin';
import { getRewardListFromMobile } from '../../../api/price/index';
import { getTeamListFromMobile, getTeamDetailFromMobile } from '../../../api/team/index';
import { getMySignUpFromMobile, deleteSignUpInfoFromMobile } from '../../../api/signUp/index';
import { getAllActType } from '@/api/Type';
import stringToColor from 'string-to-color';
export default {
  components: {
    MyTable
  },
  mixins: [requestMixin],
  inject: ['reload'],
  data() {
    return {
      active: '',
      activity: [],
      price: [],
      team: [],
      teamLength: 0,
      AllActType: [], // 存储活动类型数据
      rewardThead: [
        {
          prop: 'rewardLevel',
          label: '奖项'
        },
        {
          prop: 'rewardName',
          label: '奖励'
        },
        {
          prop: 'number',
          label: '数量'
        }
      ],
      acountThead: [
        {
          prop: 'index',
          label: '编号'
        },
        {
          prop: 'teamName',
          label: '队伍'
        },
        {
          prop: 'number',
          label: '人数'
        },
        {
          prop: 'createTime',
          label: '最终编辑时间'
        },
        {
          slotName: 'operate',
          label: '操作'
        }
      ],
      teamThead: [
        {
          prop: 'index',
          label: '序号'
        },
        {
          prop: 'name',
          label: '姓名'
        },
        {
          prop: 'remark',
          label: '备注'
        }
      ],
      showTeam: false,
      teamData: [],
      currentPage: 1,
      totalPages: 10,
      perPage: 10,
      status: 0,
      signUpInfo: {},
      appendixUrl: '',

      currentTeam: {},

      loading: true
    };
  },
  async created() {
    // 获取活动类型数据
    try {
      const response = await getAllActType();
      this.AllActType = response.data.data;
    } catch (error) {
      console.error("获取活动类型失败：", error);
    }

    if (!this.$route.query.actId) {
      this.$router.push('/list');
    } else {
      // this.getActivityDetailFromMobile();
      // this.getRewardListFromMobile();
      // this.getTeamListFromMobile();
      // this.getMySignUpFromMobile();
      this.loading = true;
      Promise.all([
        this.getActivityDetailFromMobile(),
        this.getRewardListFromMobile(),
        this.getTeamListFromMobile(),
        this.getMySignUpFromMobile()
      ]).then(() => {
        console.log(this.activity)
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    }
  },
  methods: {
    // 获取活动类型文本
    getTypeText(actType) {
      if (actType === undefined) return '';

      // 从API数据中获取标签文本
      const matchedItem = this.AllActType.find(type => type.sort === actType);
      return matchedItem ? matchedItem.label : '';
    },

    // 获取活动类型样式（基于string-to-color）
    getTypeStyle(actType) {
      if (actType === undefined) return {};

      const typeLabel = this.getTypeText(actType);
      if (!typeLabel) return {};

      // 使用string-to-color生成颜色
      const baseColor = stringToColor(typeLabel);

      return {
        color: baseColor,
        backgroundColor: `${baseColor}20`, // 20% 透明度背景
        borderColor: baseColor,
        border: `1px solid ${baseColor}`
      };
    },

    async getMySignUpFromMobile() {
      const [err, res] = await this.request(getMySignUpFromMobile, {
        actId: this.$route.query.actId
      });
      if (err) {
        return;
      }
      if (res.data.data !== undefined) {
        this.signUpInfo = res.data.data;
        this.status = res.data.data.status;
      }
    },
    async getTeamDetailFromMobile(id) {
      const [err, res] = await this.request(getTeamDetailFromMobile, {
        teamId: id
      });
      if (err) {
        return;
      }
      this.teamData = res.data.data;
    },
    async getTeamListFromMobile() {
      const [err, res] = await this.request(getTeamListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      this.team = res.data.data;
      this.teamLength = res.data.data.length;
    },
    async getRewardListFromMobile() {
      const [err, res] = await this.request(getRewardListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      this.price = res.data.data;
    },
    async getActivityDetailFromMobile() {
      this.loading = true;
      const [err, res] = await this.request(getActivityDetailFromMobile, {
        actId: this.$route.query.actId
      });
      if (err) {
        return;
      }
      this.activity = res.data.data;
      // 图片大小自适应
      this.activity.activityContentList.map(item => {
        item.content = item.content.replace(/<img/g, '<img style=\'max-width:100%;height:auto;\'');
        return item;
      });
      const appendix = res.data.data.activityDetail.enclosure;
      if (appendix !== '') {
        const index = appendix.lastIndexOf('/');
        const fileName = appendix.slice(index + 1, appendix.length);
        this.appendixUrl = `api/file/public/download?filePath=${appendix}&fileName=${fileName}&from=mobile`;
      }
      this.activity.enlistCost
        = res.data.data.activityDetail !== null
          ? res.data.data.activityDetail.enlistCost
          : 0;

      this.loading = false;
    },
    onBack() {
      if (window.history.length <= 1) {
        this.$router.push('/activityList');
      } else {
        this.$router.go(-1);
      }
    },
    onShowDetail() {
      this.$router.push({
        path: '/prizeDetail',
        query: { actId: this.$route.query.actId }
      });
    },
    onSignUp() {
      const {
        activityDetail: {
          teamPlayersMax,
          agreement,
          isTeam,
          isAllowedBring,
          bringRelativesNum
        }
      } = this.activity;
      this.$router.push({
        path: '/signup',
        query: {
          actId: this.$route.query.actId,
          teamMax: teamPlayersMax,
          agreement,
          isTeam,
          isAllowedBring,
          bringRelativesNum,
          team: JSON.stringify(this.currentTeam)
        }
      });
    },
    onShowMember(data) {
      this.getTeamDetailFromMobile(data.id);
      this.showTeam = true;
    },
    stopBubble(e) {
      e.stopPropagation();
    },
    cancel() {
      this.$dialog
        .confirm({
          message: '确定取消报名吗？'
        })
        .then(() => {
          deleteSignUpInfoFromMobile(this.signUpInfo).then((res) => {
            console.log(res)
            if (res.data.status === 0) {
              this.$toast.success('取消报名成功');
              this.reload();
            } else {
              this.$toast.fail(res.data.message);
            }
          });
        })
        .catch(() => {});
    },
    moreActivity() {
      this.$router.push('/activityList');
    },

    handleJoinTeam(data) {
      const { id, teamName } = data;
      this.currentTeam = { id, teamName };
      this.onSignUp();
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-y: auto;
  padding: 0 !important;

  .loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100vh - 46px);
    color: #fff;

    .van-loading {
      color: #fff;
    }
  }

  .content {
    background: transparent;
    padding: px2rem(12px);
    padding-bottom: px2rem(80px); // 为底部按钮留出空间

    // PC端内容区域优化 - 增加最大宽度和居中
    @media screen and (min-width: 768px) {
      max-width: 1200px;
      margin: 0 auto;
      padding: px2rem(20px);
      padding-bottom: px2rem(100px);
    }

    @media screen and (min-width: 1200px) {
      max-width: 1400px;
      padding: px2rem(32px);
      padding-bottom: px2rem(120px);
    }

    @media screen and (min-width: 1600px) {
      max-width: 1600px;
      padding: px2rem(40px);
      padding-bottom: px2rem(140px);
    }

    .activity-info {
      background: #fff;
      border-radius: px2rem(16px);
      box-shadow: 0 px2rem(8px) px2rem(32px) rgba(0, 0, 0, 0.1);
      overflow: hidden;
      margin-bottom: px2rem(16px);

      // PC端活动信息容器优化
      @media screen and (min-width: 768px) {
        border-radius: px2rem(20px);
        margin-bottom: px2rem(24px);
        box-shadow: 0 px2rem(12px) px2rem(48px) rgba(0, 0, 0, 0.08);
      }

      @media screen and (min-width: 1200px) {
        border-radius: px2rem(24px);
        margin-bottom: px2rem(32px);
        box-shadow: 0 px2rem(16px) px2rem(64px) rgba(0, 0, 0, 0.06);
      }

      .img-wrapper {
        width: 100%;
        position: relative;
        overflow: hidden;

        img {
          width: 100%;
          height: px2rem(200px);
          object-fit: cover;
          transition: transform 0.3s ease;

          // PC端图片高度优化 - 显著增大避免空白
          @media screen and (min-width: 768px) {
            height: px2rem(320px);
          }

          @media screen and (min-width: 1200px) {
            height: px2rem(400px);
          }

          @media screen and (min-width: 1600px) {
            height: px2rem(480px);
          }
        }

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: px2rem(60px);
          background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
          pointer-events: none;

          // PC端渐变高度适配
          @media screen and (min-width: 768px) {
            height: px2rem(80px);
          }

          @media screen and (min-width: 1200px) {
            height: px2rem(100px);
          }

          @media screen and (min-width: 1600px) {
            height: px2rem(120px);
          }
        }
      }

      .desc-container {
        padding: px2rem(24px) px2rem(20px);

        // PC端描述容器内边距优化
        @media screen and (min-width: 768px) {
          padding: px2rem(32px) px2rem(28px);
        }

        @media screen and (min-width: 1200px) {
          padding: px2rem(40px) px2rem(36px);
        }

        @media screen and (min-width: 1600px) {
          padding: px2rem(48px) px2rem(44px);
        }

        .title {
          display: flex;
          align-items: flex-start;
          margin-bottom: px2rem(20px);

          .name {
            flex: 1;

            .activity-title-wrapper {
              display: flex;
              align-items: center;
              gap: px2rem(12px);
              margin-bottom: px2rem(12px);

              .activity-title {
                font-size: px2rem(20px);
                font-weight: 700;
                color: #1a1a1a;
                line-height: 1.4;
                flex: 1;

                // PC端字体大小适配 - 显著增大
                @media screen and (min-width: 768px) {
                  font-size: px2rem(32px);
                }

                @media screen and (min-width: 1200px) {
                  font-size: px2rem(40px);
                }

                @media screen and (min-width: 1600px) {
                  font-size: px2rem(48px);
                }
              }

              .activity-type-tag {
                display: inline-flex;
                align-items: center;
                padding: px2rem(6px) px2rem(12px);
                border-radius: px2rem(16px);
                font-size: px2rem(11px);
                font-weight: 700;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                box-shadow: 0 px2rem(2px) px2rem(8px) rgba(0, 0, 0, 0.1);
                position: relative;

                // PC端字体大小适配 - 显著增大
                @media screen and (min-width: 768px) {
                  font-size: px2rem(16px);
                  padding: px2rem(8px) px2rem(16px);
                  border-radius: px2rem(20px);
                }

                @media screen and (min-width: 1200px) {
                  font-size: px2rem(20px);
                  padding: px2rem(10px) px2rem(20px);
                  border-radius: px2rem(24px);
                }

                @media screen and (min-width: 1600px) {
                  font-size: px2rem(24px);
                  padding: px2rem(12px) px2rem(24px);
                  border-radius: px2rem(28px);
                }
                overflow: hidden;

                &::before {
                  content: '';
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background: inherit;
                  opacity: 0.1;
                  z-index: -1;
                }

                &.type-hiking {
                  background: linear-gradient(135deg, #00b894, #00cec9);
                  color: #fff;

                  &::after {
                    content: '🥾';
                    margin-left: px2rem(4px);
                  }
                }

                &.type-custom {
                  background: linear-gradient(135deg, #6c5ce7, #a29bfe);
                  color: #fff;

                  &::after {
                    content: '🎯';
                    margin-left: px2rem(4px);
                  }
                }
              }
            }

            .status-tags > span {
              display: inline-block;
              padding: px2rem(4px) px2rem(12px);
              border-radius: px2rem(20px);
              font-size: px2rem(12px);
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-right: px2rem(8px);

              // PC端字体大小适配 - 显著增大
              @media screen and (min-width: 768px) {
                font-size: px2rem(18px);
                padding: px2rem(6px) px2rem(16px);
                border-radius: px2rem(24px);
              }

              @media screen and (min-width: 1200px) {
                font-size: px2rem(22px);
                padding: px2rem(8px) px2rem(20px);
                border-radius: px2rem(28px);
              }

              @media screen and (min-width: 1600px) {
                font-size: px2rem(26px);
                padding: px2rem(10px) px2rem(24px);
                border-radius: px2rem(32px);
              }
            }

            .activity-not-start {
              background: linear-gradient(135deg, #ffeaa7, #fab1a0);
              color: #2d3436;
            }

            .activity-sign-up {
              background: linear-gradient(135deg, #00b894, #00cec9);
              color: #fff;
              animation: pulse 2s infinite;
            }

            .activity-sign-up-end {
              background: linear-gradient(135deg, #e17055, #d63031);
              color: #fff;
            }

            .activity-ongoing {
              background: linear-gradient(135deg, #0984e3, #74b9ff);
              color: #fff;
            }

            .activity-finish {
              background: linear-gradient(135deg, #636e72, #b2bec3);
              color: #fff;
            }
          }

          .appendix {
            margin-left: px2rem(12px);

            a {
              display: inline-flex;
              align-items: center;
              padding: px2rem(8px) px2rem(16px);
              background: linear-gradient(135deg, $color-primary, $color-primary-light);
              color: #fff;
              border-radius: px2rem(20px);
              font-size: px2rem(12px);
              font-weight: 500;
              text-decoration: none;
              box-shadow: 0 px2rem(4px) px2rem(12px) rgba($color-primary, 0.3);
              transition: all 0.3s ease;

              // PC端字体大小适配 - 显著增大
              @media screen and (min-width: 768px) {
                font-size: px2rem(18px);
                padding: px2rem(12px) px2rem(24px);
                border-radius: px2rem(24px);
              }

              @media screen and (min-width: 1200px) {
                font-size: px2rem(22px);
                padding: px2rem(14px) px2rem(28px);
                border-radius: px2rem(28px);
              }

              @media screen and (min-width: 1600px) {
                font-size: px2rem(26px);
                padding: px2rem(16px) px2rem(32px);
                border-radius: px2rem(32px);
              }

              &:hover {
                transform: translateY(px2rem(-2px));
                box-shadow: 0 px2rem(6px) px2rem(16px) rgba($color-primary, 0.4);
              }

              &::before {
                content: '📎';
                margin-right: px2rem(4px);
              }
            }
          }
        }

        .list-item {
          display: flex;
          align-items: center;
          padding: px2rem(16px) px2rem(20px);
          margin: px2rem(8px) px2rem(-20px);
          border-radius: px2rem(12px);
          background: rgba($color-primary, 0.02);
          transition: all 0.3s ease;

          &:hover {
            background: rgba($color-primary, 0.05);
            transform: translateX(px2rem(4px));
          }

          // PC端列表项间距优化
          @media screen and (min-width: 768px) {
            padding: px2rem(20px) px2rem(28px);
            margin: px2rem(12px) px2rem(-28px);
            border-radius: px2rem(16px);
          }

          @media screen and (min-width: 1200px) {
            padding: px2rem(24px) px2rem(36px);
            margin: px2rem(16px) px2rem(-36px);
            border-radius: px2rem(20px);
          }

          @media screen and (min-width: 1600px) {
            padding: px2rem(28px) px2rem(44px);
            margin: px2rem(20px) px2rem(-44px);
            border-radius: px2rem(24px);
          }

          .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: px2rem(40px);
            height: px2rem(40px);
            background: linear-gradient(135deg, $color-primary, $color-primary-light);
            color: #fff;
            border-radius: px2rem(20px);
            font-size: px2rem(18px);
            margin-right: px2rem(16px);
            box-shadow: 0 px2rem(4px) px2rem(12px) rgba($color-primary, 0.2);

            // PC端图标大小适配 - 显著增大
            @media screen and (min-width: 768px) {
              width: px2rem(56px);
              height: px2rem(56px);
              font-size: px2rem(28px);
              border-radius: px2rem(28px);
              margin-right: px2rem(20px);
            }

            @media screen and (min-width: 1200px) {
              width: px2rem(64px);
              height: px2rem(64px);
              font-size: px2rem(32px);
              border-radius: px2rem(32px);
              margin-right: px2rem(24px);
            }

            @media screen and (min-width: 1600px) {
              width: px2rem(72px);
              height: px2rem(72px);
              font-size: px2rem(36px);
              border-radius: px2rem(36px);
              margin-right: px2rem(28px);
            }
          }

          .text {
            flex: 1;
            font-size: px2rem(14px);
            color: #2d3436;
            line-height: 1.5;
            font-weight: 500;

            // PC端字体大小适配 - 显著增大
            @media screen and (min-width: 768px) {
              font-size: px2rem(20px);
            }

            @media screen and (min-width: 1200px) {
              font-size: px2rem(24px);
            }

            @media screen and (min-width: 1600px) {
              font-size: px2rem(28px);
            }
          }
        }
      }
    }
    .activity-count {
      background: #fff;
      border-radius: px2rem(16px);
      box-shadow: 0 px2rem(8px) px2rem(32px) rgba(0, 0, 0, 0.1);
      padding: px2rem(24px) px2rem(20px);
      margin-bottom: px2rem(16px);

      // PC端活动统计容器优化
      @media screen and (min-width: 768px) {
        border-radius: px2rem(20px);
        padding: px2rem(32px) px2rem(28px);
        margin-bottom: px2rem(24px);
        box-shadow: 0 px2rem(12px) px2rem(48px) rgba(0, 0, 0, 0.08);
      }

      @media screen and (min-width: 1200px) {
        border-radius: px2rem(24px);
        padding: px2rem(40px) px2rem(36px);
        margin-bottom: px2rem(32px);
        box-shadow: 0 px2rem(16px) px2rem(64px) rgba(0, 0, 0, 0.06);
      }

      @media screen and (min-width: 1600px) {
        padding: px2rem(48px) px2rem(44px);
        margin-bottom: px2rem(40px);
      }

      .signup-number {
        display: flex;
        align-items: center;
        padding: px2rem(16px) px2rem(20px);
        background: linear-gradient(135deg, rgba($color-primary, 0.1), rgba($color-secondary, 0.1));
        border-radius: px2rem(12px);
        margin-bottom: px2rem(16px);

        .decorate {
          width: px2rem(6px);
          height: px2rem(40px);
          background: linear-gradient(135deg, $color-primary, $color-secondary);
          border-radius: px2rem(3px);
          margin-right: px2rem(16px);
        }

        .signup-number-title {
          flex: 1;
          font-size: px2rem(14px);
          color: #2d3436;
          font-weight: 600;

          // PC端字体大小适配 - 显著增大
          @media screen and (min-width: 768px) {
            font-size: px2rem(20px);
          }

          @media screen and (min-width: 1200px) {
            font-size: px2rem(24px);
          }

          @media screen and (min-width: 1600px) {
            font-size: px2rem(28px);
          }
        }

        .signup-number-detail {
          font-size: px2rem(16px);
          font-weight: 700;
          color: $color-primary;
          background: #fff;
          padding: px2rem(8px) px2rem(16px);
          border-radius: px2rem(20px);
          box-shadow: 0 px2rem(2px) px2rem(8px) rgba($color-primary, 0.2);

          // PC端字体大小适配 - 显著增大
          @media screen and (min-width: 768px) {
            font-size: px2rem(22px);
            padding: px2rem(12px) px2rem(20px);
            border-radius: px2rem(24px);
          }

          @media screen and (min-width: 1200px) {
            font-size: px2rem(26px);
            padding: px2rem(14px) px2rem(24px);
            border-radius: px2rem(28px);
          }

          @media screen and (min-width: 1600px) {
            font-size: px2rem(30px);
            padding: px2rem(16px) px2rem(28px);
            border-radius: px2rem(32px);
          }
        }
      }
    }

    .activity-instructions {
      background: #fff;
      border-radius: px2rem(16px);
      box-shadow: 0 px2rem(8px) px2rem(32px) rgba(0, 0, 0, 0.1);
      padding: px2rem(24px) px2rem(20px);
      margin-bottom: px2rem(80px); // 为底部按钮留出空间
      font-size: px2rem(18px);

      // PC端字体大小适配 - 显著增大
      @media screen and (min-width: 768px) {
        font-size: px2rem(24px);
        padding: px2rem(32px) px2rem(28px);
        border-radius: px2rem(20px);
        margin-bottom: px2rem(100px);
        box-shadow: 0 px2rem(12px) px2rem(48px) rgba(0, 0, 0, 0.08);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(28px);
        padding: px2rem(40px) px2rem(36px);
        border-radius: px2rem(24px);
        margin-bottom: px2rem(120px);
        box-shadow: 0 px2rem(16px) px2rem(64px) rgba(0, 0, 0, 0.06);
      }

      @media screen and (min-width: 1600px) {
        font-size: px2rem(32px);
        padding: px2rem(48px) px2rem(44px);
        margin-bottom: px2rem(140px);
      }
      ::v-deep .van-tabs {
        .van-tabs__wrap {
          background: rgba($color-primary, 0.05);
          border-radius: px2rem(12px);
          padding: px2rem(4px);
          margin-bottom: px2rem(20px);
        }

        .van-tab {
          border-radius: px2rem(8px);
          font-weight: 500;
          transition: all 0.3s ease;

          &.van-tab--active {
            background: #fff;
            color: $color-primary;
            box-shadow: 0 px2rem(2px) px2rem(8px) rgba($color-primary, 0.2);
          }
        }

        .van-tabs__line {
          display: none; // 隐藏默认的下划线
        }

        .van-tabs__content {
          padding-top: px2rem(16px);

          article {
            line-height: 1.6;
            color: #2d3436;

            img {
              border-radius: px2rem(8px);
              box-shadow: 0 px2rem(4px) px2rem(16px) rgba(0, 0, 0, 0.1);
              margin: px2rem(12px) 0;
            }


            h1, h2, h3, h4, h5, h6 {
              color: #1a1a1a;
              margin: px2rem(20px) 0 px2rem(12px) 0;
              font-weight: 700;
            }
          }
        }
      }
    }
  }

  // 底部操作栏美化
  ::v-deep .van-goods-action {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(px2rem(10px));
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 px2rem(-4px) px2rem(20px) rgba(0, 0, 0, 0.1);

    .van-goods-action-icon {
      background: transparent;
      color: $color-primary;
      font-weight: 600;

      .van-icon {
        font-size: px2rem(20px);
        margin-bottom: px2rem(4px);

        // PC端图标大小适配 - 显著增大
        @media screen and (min-width: 768px) {
          font-size: px2rem(28px);
          margin-bottom: px2rem(6px);
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(32px);
          margin-bottom: px2rem(8px);
        }

        @media screen and (min-width: 1600px) {
          font-size: px2rem(36px);
          margin-bottom: px2rem(10px);
        }
      }
    }

    .van-goods-action-button {
      border-radius: px2rem(25px) px2rem(25px) 0 0;
      font-weight: 600;
      font-size: px2rem(16px);

      // PC端字体大小适配 - 显著增大
      @media screen and (min-width: 768px) {
        font-size: px2rem(22px);
        border-radius: px2rem(30px) px2rem(30px) 0 0;
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(26px);
        border-radius: px2rem(35px) px2rem(35px) 0 0;
      }

      @media screen and (min-width: 1600px) {
        font-size: px2rem(30px);
        border-radius: px2rem(40px) px2rem(40px) 0 0;
      }

      &--primary {
        background: linear-gradient(135deg, $color-primary, $color-primary-light);
        box-shadow: 0 px2rem(-2px) px2rem(12px) rgba($color-primary, 0.3);
      }

      &--danger {
        background: linear-gradient(135deg, #e17055, #d63031);
      }
    }
  }

  .footer {
    z-index: 99;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
  }
}

// 添加动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba($color-secondary, 0.7);
  }
  70% {
    box-shadow: 0 0 0 px2rem(10px) rgba($color-secondary, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba($color-secondary, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(px2rem(30px));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 为内容添加进入动画
.content > * {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.activity-info {
  animation-delay: 0.1s;
}

.activity-count {
  animation-delay: 0.2s;
}

.activity-instructions {
  animation-delay: 0.3s;
}

::v-deep .team-btn {
  min-width: 50px;
  height: 30px;
  padding: 0 10px;
}
</style>
