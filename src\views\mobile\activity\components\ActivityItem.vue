<template>
  <div class="modern-activity-item" @click="onShowDetail">
    <!-- 活动图片 -->
    <div class="activity-image">
      <van-image
        width="100%"
        height="100%"
        fit="cover"
        :src="'/image/'+activity.actImg"
        :show-loading="true"
        :show-error="true"
        @click="previewImage"
      >
        <template v-slot:loading>
          <div class="image-loading">
            <van-loading type="spinner" size="20px" />
            <span>加载中...</span>
          </div>
        </template>
        <template v-slot:error>
          <div class="image-error">
            <van-icon name="photo-fail" size="24px" />
            <span>加载失败</span>
            <van-button
              type="primary"
              size="mini"
              @click.stop="retryLoadImage"
              class="retry-btn"
            >
              重试
            </van-button>
          </div>
        </template>
      </van-image>

      <!-- 状态标签 -->
      <div class="status-badge" :class="getStatusClass()">
        <span class="status-text">{{ getStatusText() }}</span>
      </div>

      <!-- 活动类型标签 -->
      <div
        v-if="activity.actType !== undefined"
        class="activity-type-badge"
        :class="getTypeClass()"
        :style="getTypeStyle()"
      >
        <span class="type-icon">{{ getTypeIcon() }}</span>
        <span class="type-text">{{ getTypeText() }}</span>
      </div>
    </div>

    <!-- 活动内容 -->
    <div class="activity-content">
      <!-- 活动标题 -->
      <h3 class="activity-title">{{ activity.actName }}</h3>

      <!-- 活动信息 -->
      <div class="activity-meta">
        <!-- 时间信息 -->
        <div class="meta-item">
          <div class="meta-icon">
            <van-icon name="clock-o" />
          </div>
          <div class="meta-content">
            <span class="meta-label">活动时间</span>
            <span class="meta-value">{{ formatDate(activity.actStartTime) }}</span>
          </div>
        </div>

        <!-- 地点信息 -->
        <div class="meta-item">
          <div class="meta-icon">
            <van-icon name="location-o" />
          </div>
          <div class="meta-content">
            <span class="meta-label">活动地点</span>
            <span class="meta-value">{{ activity.address }}</span>
          </div>
        </div>
      </div>

      <!-- 报名进度 -->
      <div class="signup-progress">
        <div class="progress-header">
          <div class="progress-info">
            <van-icon name="friends-o" />
            <span>报名进度</span>
          </div>
          <div class="progress-numbers">
            <span class="current">{{ activity.participationNum }}</span>
            <span class="separator">/</span>
            <span class="total">{{ activity.actNum }}</span>
          </div>
        </div>
        <div class="progress-bar">
          <van-progress
            :percentage="getProgressPercentage()"
            :show-pivot="false"
            color="linear-gradient(135deg, #667eea, #764ba2)"
            track-color="rgba(102, 126, 234, 0.1)"
          />
        </div>
      </div>

      <!-- 倒计时 -->
      <div class="countdown-section" v-if="countdown">
        <div class="countdown-icon">
          <van-icon name="clock" />
        </div>
        <div class="countdown-text">{{ countdown }}</div>
      </div>
    </div>

    <!-- 悬浮效果 -->
    <div class="hover-overlay"></div>
  </div>
</template>

<script>
import { getAllActType } from '@/api/Type';
import stringToColor from 'string-to-color';

export default {
  props: {
    activity: {
      type: Object,
      required: true // 修正拼写错误：require → required
    },
  },
  data() {
    return {
      countdown: '', // 存储倒计时显示文本
      countdownTimer: null, // 存储定时器ID
      AllActType: [] // 存储活动类型数据
    }
  },
  async created() {
    try {
      const response = await getAllActType();
      this.AllActType = response.data.data;
    } catch (error) {
      console.error("获取活动类型失败：", error);
    }
  },
  mounted() {
    // 组件挂载后打印 props
    console.log('接收的 activity props:', this.activity);
    
    // 初始化倒计时
    this.updateCountdown();
    this.countdownTimer = setInterval(() => {
      this.updateCountdown();
    }, 1000); // 每秒更新一次
  },
  beforeDestroy() {
    // 组件销毁前清除定时器，避免内存泄漏
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
  },
  methods: {
    onShowDetail() {
      // 跳转到活动详情页
      this.$router.push({
        path: '/activityDetail/',
        query: { actId: this.activity.id }
      });
    },

    // 预览图片
    previewImage() {
      if (!this.activity.actImg) {
        this.$toast('暂无图片可预览');
        return;
      }
      // 使用 Vant 的图片预览功能
      this.$imagePreview(['/image/' + this.activity.actImg]);
    },

    // 重试加载图片
    retryLoadImage() {
      // 强制重新加载图片
      this.$nextTick(() => {
        const imageElement = this.$el.querySelector('.van-image img');
        if (imageElement) {
          const src = imageElement.src;
          imageElement.src = '';
          setTimeout(() => {
            imageElement.src = src + '?t=' + Date.now();
          }, 100);
        }
      });
    },


    getStatusClass() {
      const statusMap = {
        1: 'status-not-started',
        2: 'status-signup',
        3: 'status-signup-end',
        4: 'status-ongoing',
        5: 'status-finished'
      };
      return statusMap[this.activity.status] || 'status-unknown';
    },

    getStatusText() {
      const statusMap = {
        1: '未开始',
        2: '报名中',
        3: '报名截止',
        4: '进行中',
        5: '已结束'
      };
      return statusMap[this.activity.status] || '未知';
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
      const weekday = weekdays[date.getDay()];
      return `${month}月${day}日 周${weekday}`;
    },

    getProgressPercentage() {
      if (!this.activity.actNum || this.activity.actNum === 0) return 0;
      const percentage = (this.activity.participationNum / this.activity.actNum) * 100;
      return Math.min(percentage, 100);
    },

    getTypeClass() {
      if (this.activity.actType === undefined) {
        return '';
      }
      // 基于活动类型标签生成class名
      const typeLabel = this.getTypeText();
      return typeLabel ? `type-${typeLabel.replace(/\s+/g, '-').toLowerCase()}` : '';
    },

    getTypeIcon() {
      if (this.activity.actType === undefined) {
        return '';
      }
      // 从API数据中获取图标，如果没有则使用默认图标
      const matchedItem = this.AllActType.find(type => type.sort === this.activity.actType);
      return matchedItem && matchedItem.icon ? matchedItem.icon : '📋';
    },

    getTypeText() {
      if (this.activity.actType === undefined) {
        return '';
      }
      // 从API数据中获取标签文本
      const matchedItem = this.AllActType.find(type => type.sort === this.activity.actType);
      return matchedItem ? matchedItem.label : '';
    },

    // 获取活动类型样式（基于string-to-color）
    getTypeStyle() {
      if (this.activity.actType === undefined) return {};

      const typeLabel = this.getTypeText();
      if (!typeLabel) return {};

      // 使用string-to-color生成颜色
      const baseColor = stringToColor(typeLabel);

      return {
        color: baseColor,
        backgroundColor: `${baseColor}20`, // 20% 透明度背景
        borderColor: baseColor
      };
    },
    updateCountdown() {
      // 获取活动开始,结束时间;报名开始时间,结束时间。
      const actStartTime = new Date(this.activity.actStartTime);
      const actEndTime = new Date(this.activity.actEndTime);
      const enlistStartTime = new Date(this.activity.enlistStartTime);
      const enlistEndTime = new Date(this.activity.enlistEndTime);
      const now = new Date();
      
      // 计算活动状态和剩余时间
      if (now < enlistStartTime) {
        // 报名未开始，计算报名开始的剩余时间
        const diff = enlistStartTime - now;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        this.countdown = `报名倒计时：${days}天${hours}`;
      } else if (now <= enlistEndTime) {
        const diff = enlistEndTime - now;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        this.countdown = `报名剩余时间：${days}天${hours}小时`;
      } else if(now <= actStartTime){
        const diff = actStartTime - now;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        this.countdown = `活动开始倒计时：${days}天${hours}小时`;
      } else if(now <= actEndTime){
        const diff = actEndTime - now;
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        this.countdown = `活动结束时间：${days}天${hours}小时`;
      }else{
        // 活动已结束
        this.countdown = '活动已结束';
        // console.log(this.countdown)
        // 活动结束后停止倒计时更新
        clearInterval(this.countdownTimer);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.modern-activity-item {
  position: relative;
  background: #fff;
  border-radius: px2rem(16px);
  overflow: hidden;
  box-shadow: 0 px2rem(8px) px2rem(32px) rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(px2rem(-4px));
    box-shadow: 0 px2rem(16px) px2rem(48px) rgba(0, 0, 0, 0.12);

    .hover-overlay {
      opacity: 1;
    }
  }

  // 活动图片
  .activity-image {
    position: relative;
    width: 100%;
    height: px2rem(200px); // 增加高度
    min-height: px2rem(200px); // 设置最小高度
    overflow: hidden;

    .van-image {
      width: 100%;
      height: 100%;
      min-height: px2rem(200px);
      transition: transform 0.3s ease;

      ::v-deep img {
        width: 100%;
        height: 100%;
        min-height: px2rem(200px);
        object-fit: cover;
        object-position: center;
      }
    }

    &:hover .van-image {
      transform: scale(1.02); // 减少缩放幅度，避免移动端过度动画
    }

    .image-loading,
    .image-error {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background: #f5f5f5;
      color: #ccc;

      span {
        font-size: px2rem(12px);
        margin-top: px2rem(8px);
        color: #999;
      }

      .retry-btn {
        margin-top: px2rem(8px);
        font-size: px2rem(10px);
        padding: px2rem(4px) px2rem(8px);
      }
    }

    // 状态标签
    .status-badge {
      position: absolute;
      top: px2rem(12px);
      left: px2rem(12px);
      padding: px2rem(6px) px2rem(12px);
      border-radius: px2rem(20px);
      font-size: px2rem(12px);
      font-weight: 600;
      backdrop-filter: blur(px2rem(10px));

      // PC端字体大小适配 - 进一步增大
      @media screen and (min-width: 768px) {
        font-size: px2rem(18px);
        padding: px2rem(10px) px2rem(18px);
        border-radius: px2rem(26px);
        top: px2rem(16px);
        left: px2rem(16px);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(22px);
        padding: px2rem(12px) px2rem(24px);
        border-radius: px2rem(32px);
        top: px2rem(20px);
        left: px2rem(20px);
      }

      @media screen and (min-width: 1600px) {
        font-size: px2rem(26px);
        padding: px2rem(16px) px2rem(30px);
        border-radius: px2rem(38px);
        top: px2rem(24px);
        left: px2rem(24px);
      }

      .status-text {
        text-shadow: 0 px2rem(1px) px2rem(2px) rgba(0, 0, 0, 0.1);
      }

      &.status-not-started {
        background: rgba(108, 117, 125, 0.9);
        color: #fff;
      }

      &.status-signup {
        background: rgba(40, 167, 69, 0.9);
        color: #fff;
        animation: pulse 2s infinite;
      }

      &.status-signup-end {
        background: rgba(220, 53, 69, 0.9);
        color: #fff;
      }

      &.status-ongoing {
        background: rgba(0, 123, 255, 0.9);
        color: #fff;
      }

      &.status-finished {
        background: rgba(108, 117, 125, 0.9);
        color: #fff;
      }
    }

    // 活动类型标签
    .activity-type-badge {
      position: absolute;
      top: px2rem(12px);
      right: px2rem(12px);
      display: flex;
      align-items: center;
      padding: px2rem(6px) px2rem(10px);
      border-radius: px2rem(16px);
      font-size: px2rem(11px);
      font-weight: 600;
      backdrop-filter: blur(px2rem(10px));
      box-shadow: 0 px2rem(2px) px2rem(8px) rgba(0, 0, 0, 0.1);

      // PC端字体大小适配 - 进一步增大
      @media screen and (min-width: 768px) {
        font-size: px2rem(17px);
        padding: px2rem(10px) px2rem(16px);
        border-radius: px2rem(22px);
        top: px2rem(16px);
        right: px2rem(16px);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(20px);
        padding: px2rem(12px) px2rem(22px);
        border-radius: px2rem(28px);
        top: px2rem(20px);
        right: px2rem(20px);
      }

      @media screen and (min-width: 1600px) {
        font-size: px2rem(24px);
        padding: px2rem(16px) px2rem(28px);
        border-radius: px2rem(34px);
        top: px2rem(24px);
        right: px2rem(24px);
      }

      .type-icon {
        margin-right: px2rem(4px);
        font-size: px2rem(12px);

        // PC端图标大小适配 - 进一步增大
        @media screen and (min-width: 768px) {
          font-size: px2rem(18px);
          margin-right: px2rem(7px);
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(22px);
          margin-right: px2rem(8px);
        }

        @media screen and (min-width: 1600px) {
          font-size: px2rem(26px);
          margin-right: px2rem(10px);
        }
      }

      .type-text {
        text-shadow: 0 px2rem(1px) px2rem(2px) rgba(0, 0, 0, 0.1);
      }

      &.type-hiking {
        background: rgba(0, 184, 148, 0.9);
        color: #fff;
      }

      &.type-custom {
        background: rgba(108, 92, 231, 0.9);
        color: #fff;
      }
    }
  }

  // 活动内容
  .activity-content {
    padding: px2rem(20px);

    // 活动标题
    .activity-title {
      font-size: px2rem(20px);
      font-weight: 700;
      color: #1a1a1a;
      margin: 0 0 px2rem(16px) 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;

      // PC端字体大小适配 - 增加一倍
      @media screen and (min-width: 768px) {
        font-size: px2rem(36px);
        margin: 0 0 px2rem(22px) 0;
        line-height: 1.3;
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(36px);
        margin: 0 0 px2rem(28px) 0;
      }

      @media screen and (min-width: 1600px) {
        font-size: px2rem(44px);
        margin: 0 0 px2rem(32px) 0;
      }
    }

    // 活动信息
    .activity-meta {
      margin-bottom: px2rem(16px);

      .meta-item {
        display: flex;
        align-items: center;
        margin-bottom: px2rem(12px);
        padding: px2rem(8px);
        background: rgba(255, 255, 255, 0.6);
        border-radius: px2rem(8px);
        border: 1px solid rgba(102, 126, 234, 0.08);
        transition: all 0.3s ease;
        position: relative;

        // PC端整体容器适配
        @media screen and (min-width: 768px) {
          margin-bottom: 8px;
          padding: 6px;
          border-radius: 6px;
        }

        @media screen and (min-width: 1200px) {
          margin-bottom: 6px;
          padding: 4px;
          border-radius: 4px;
        }

        @media screen and (min-width: 1600px) {
          margin-bottom: 6px;
          padding: 4px;
          border-radius: 4px;
        }

        &:last-child {
          margin-bottom: 0;
        }

        // 轻微的悬浮效果
        &:hover {
          background: rgba(102, 126, 234, 0.03);
          border-color: rgba(102, 126, 234, 0.12);
          transform: translateY(px2rem(-1px));
        }

        .meta-icon {
          width: px2rem(32px);
          height: px2rem(32px);
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
          border-radius: px2rem(16px);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: px2rem(12px);
          flex-shrink: 0;

          // PC端图标容器适配
          @media screen and (min-width: 768px) {
            width: 20px;
            height: 20px;
            border-radius: 10px;
            margin-right: 6px;
          }

          @media screen and (min-width: 1200px) {
            width: 22px;
            height: 22px;
            border-radius: 11px;
            margin-right: 8px;
          }

          @media screen and (min-width: 1600px) {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            margin-right: 8px;
          }

          .van-icon {
            font-size: px2rem(16px);
            color: #667eea;

            // PC端图标大小适配
            @media screen and (min-width: 768px) {
              font-size: 10px;
            }

            @media screen and (min-width: 1200px) {
              font-size: 11px;
            }

            @media screen and (min-width: 1600px) {
              font-size: 12px;
            }
          }
        }

        .meta-content {
          flex: 1;
          line-height: 1.5;
          position: relative;
          z-index: 1;

          .meta-label {
            display: block;
            font-size: px2rem(13px);
            color: #666;
            margin-bottom: px2rem(6px);
            line-height: 1.4;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: px2rem(0.5px);

            // PC端标签适配
            @media screen and (min-width: 768px) {
              font-size: 9px;
              margin-bottom: 2px;
              letter-spacing: 0.2px;
            }

            @media screen and (min-width: 1200px) {
              font-size: 10px;
              margin-bottom: 2px;
              letter-spacing: 0.3px;
            }

            @media screen and (min-width: 1600px) {
              font-size: 11px;
              margin-bottom: 3px;
              letter-spacing: 0.3px;
            }
          }

          .meta-value {
            font-size: px2rem(16px);
            color: #1a1a1a;
            font-weight: 600;
            line-height: 1.4;
            word-break: break-all;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            // PC端值适配
            @media screen and (min-width: 768px) {
              font-size: 11px;
              font-weight: 600;
              line-height: 1.3;
            }

            @media screen and (min-width: 1200px) {
              font-size: 12px;
              font-weight: 600;
              line-height: 1.3;
            }

            @media screen and (min-width: 1600px) {
              font-size: 13px;
              font-weight: 600;
              line-height: 1.2;
            }
          }
        }
      }
    }

    // 报名进度
    .signup-progress {
      margin-bottom: px2rem(16px);

      .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: px2rem(8px);

        .progress-info {
          display: flex;
          align-items: center;
          font-size: px2rem(14px);
          color: #666;

          // PC端字体大小适配 - 进一步增大
          @media screen and (min-width: 768px) {
            font-size: px2rem(20px);
          }

          @media screen and (min-width: 1200px) {
            font-size: px2rem(24px);
          }

          @media screen and (min-width: 1600px) {
            font-size: px2rem(28px);
          }

          .van-icon {
            font-size: px2rem(16px);
            margin-right: px2rem(6px);
            color: #667eea;

            // PC端图标大小适配 - 进一步增大
            @media screen and (min-width: 768px) {
              font-size: px2rem(22px);
              margin-right: px2rem(10px);
            }

            @media screen and (min-width: 1200px) {
              font-size: px2rem(26px);
              margin-right: px2rem(12px);
            }

            @media screen and (min-width: 1600px) {
              font-size: px2rem(30px);
              margin-right: px2rem(14px);
            }
          }
        }

        .progress-numbers {
          font-size: px2rem(14px);
          font-weight: 600;

          // PC端字体大小适配 - 进一步增大
          @media screen and (min-width: 768px) {
            font-size: px2rem(20px);
          }

          @media screen and (min-width: 1200px) {
            font-size: px2rem(24px);
          }

          @media screen and (min-width: 1600px) {
            font-size: px2rem(28px);
          }

          .current {
            color: #667eea;
          }

          .separator {
            color: #ccc;
            margin: 0 px2rem(4px);

            // PC端间距适配
            @media screen and (min-width: 768px) {
              margin: 0 px2rem(6px);
            }

            @media screen and (min-width: 1200px) {
              margin: 0 px2rem(8px);
            }
          }

          .total {
            color: #999;
          }
        }
      }

      .progress-bar {
        ::v-deep .van-progress {
          .van-progress__portion {
            border-radius: px2rem(6px);
          }

          .van-progress__track {
            border-radius: px2rem(6px);
            height: px2rem(6px);
          }
        }
      }
    }

    // 倒计时
    .countdown-section {
      display: flex;
      align-items: center;
      padding: px2rem(12px) px2rem(16px);
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
      border-radius: px2rem(12px);
      border-left: px2rem(4px) solid #667eea;

      .countdown-icon {
         display: flex;
        margin-right: px2rem(8px);
        align-items: center;

        .van-icon {
          font-size: px2rem(16px);
          color: #667eea;

          // PC端图标大小适配 - 进一步增大
          @media screen and (min-width: 768px) {
            font-size: px2rem(20px);
          }

          @media screen and (min-width: 1200px) {
            font-size: px2rem(24px);
          }

          @media screen and (min-width: 1600px) {
            font-size: px2rem(28px);
          }
        }
      }

      .countdown-text {
        font-size: px2rem(13px);
        color: #667eea;

        // PC端字体大小适配 - 进一步增大
        @media screen and (min-width: 768px) {
          font-size: px2rem(18px);
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(22px);
        }

        @media screen and (min-width: 1600px) {
          font-size: px2rem(26px);
        }
        font-weight: 600;
      }
    }
  }

  // 悬浮效果
  .hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 px2rem(10px) rgba(40, 167, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

// 响应式适配
@media screen and (max-width: 375px) {
  .modern-activity-item {
    .activity-image {
      height: px2rem(160px);
    }

    .activity-content {
      padding: px2rem(16px);

      .activity-title {
        font-size: px2rem(16px);
      }

      .meta-item {
        .meta-icon {
          width: px2rem(28px);
          height: px2rem(28px);

          .van-icon {
            font-size: px2rem(14px);
          }
        }

        .meta-content {
          line-height: 1.4;

          .meta-label {
            font-size: px2rem(11px);
            margin-bottom: px2rem(2px);
            line-height: 1.3;
          }

          .meta-value {
            font-size: px2rem(13px);
            line-height: 1.4;
          }
        }
      }
    }
  }
}

// PC端适配 - 平板及以上设备
@media screen and (min-width: 768px) {
  .modern-activity-item {
    margin-bottom: 24px;
    border-radius: 16px;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
    }

    .activity-image {
      height: 240px;
      border-radius: 16px 16px 0 0;
    }

    .activity-content {
      padding: 20px;

      .activity-header {
        margin-bottom: 16px;

        .activity-title {
          font-size: 18px;
          line-height: 1.4;
          margin-bottom: 6px;
        }

        .activity-description {
          font-size: 14px;
          line-height: 1.5;
        }
      }

      .activity-meta {
        margin-bottom: 16px;


      }

      .activity-footer {
        .activity-tags {
          gap: 8px;
          margin-bottom: 16px;

          .activity-tag {
            padding: 4px 12px;
            font-size: 12px;
            border-radius: 16px;
          }
        }

        .activity-actions {
          .action-button {
            padding: 12px 24px;
            font-size: 18px;
            border-radius: 8px;
            min-width: 120px;
          }
        }
      }

      .progress-section {
        margin-top: 16px;

        .progress-info {
          margin-bottom: 8px;

          .progress-text {
            font-size: 13px;
          }

          .progress-numbers {
            font-size: 13px;
          }
        }

        .progress-bar {
          height: 6px;
          border-radius: 3px;
        }
      }

      .countdown-section {
        margin-top: 16px;

        .countdown-label {
          font-size: 13px;
          margin-bottom: 6px;
        }

        .countdown-display {
          .countdown-item {
            .countdown-number {
              font-size: 16px;
              padding: 6px 10px;
              border-radius: 4px;
            }

            .countdown-unit {
              font-size: 11px;
            }
          }

          .countdown-separator {
            font-size: 14px;
            margin: 0 6px;
          }
        }
      }
    }
  }
}

// 大屏PC端适配
@media screen and (min-width: 1200px) {
  .modern-activity-item {
    .activity-image {
      height: 280px;
    }

    .activity-content {
      padding: 24px;

      .activity-header {
        margin-bottom: 18px;

        .activity-title {
          font-size: 20px;
          margin-bottom: 8px;
        }

        .activity-description {
          font-size: 15px;
        }
      }

      .activity-meta {
        margin-bottom: 20px;


      }

      .activity-footer {
        .activity-tags {
          gap: 10px;
          margin-bottom: 18px;

          .activity-tag {
            padding: 5px 14px;
            font-size: 13px;
            border-radius: 18px;
          }
        }

        .activity-actions {
          .action-button {
            padding: 16px 32px;
            font-size: 20px;
            min-width: 140px;
            border-radius: 10px;
          }
        }
      }

      .countdown-section {
        margin-top: 18px;

        .countdown-label {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .countdown-display {
          .countdown-item {
            .countdown-number {
              font-size: 18px;
              padding: 8px 12px;
              border-radius: 5px;
            }

            .countdown-unit {
              font-size: 12px;
            }
          }

          .countdown-separator {
            font-size: 16px;
            margin: 0 8px;
          }
        }
      }
    }
  }
}
</style>
