//富文本样式

// 字体缩进尺寸
$text-indent: (
  1:1,
  2:2,
  3:3,
  4:4,
  5:5,
  6:6,
  7:7,
  8:8
);
$base-text-indent: 2em;
@each $key,$size in $text-indent {
  .ql-indent-#{$key} {
    text-indent: $size*$base-text-indent;
  }
}



//字体浮动
@each $var in (left, center, right) {
  //(left,center,right)这是一个集合
  .ql-align-#{$var} {
    //相当于.ql-align-left .ql-align-center .ql-align-right
    text-align: $var;
  }
}