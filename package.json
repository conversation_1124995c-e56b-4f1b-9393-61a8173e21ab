{"name": "signing_up", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "commit": "git add . && git cz", "lint": "vue-cli-service lint", "start": "node index.js", "server": "nodemon index.js --ignore client"}, "dependencies": {"axios": "^0.19.0", "core-js": "^3.6.5", "element-ui": "^2.13.2", "font-awesome": "^4.7.0", "string-to-color": "^2.2.2", "vant": "^2.10.8", "vue": "^2.6.11", "vue-qr": "^4.0.9", "vue-router": "^3.2.0", "vue2-editor": "^2.10.2", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "sass": "^1.32.13", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}