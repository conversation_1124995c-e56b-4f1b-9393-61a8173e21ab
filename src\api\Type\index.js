import { getRequest, postRequest, putRequest, deleteRequest } from '../../assets/js/request';

import { baseUrl } from '../../config/constants';

// 获取所有活动类型
export const getAllActType = () => {
  return getRequest(`${baseUrl}/public/dictData/AllActType`);
};

// 分页获取活动类型表格数据
export const getActTypeTable = (pageNumber, pageSize) => {
  return getRequest(`${baseUrl}/public/dictData/ActTypeTable`, {
    pageNumber,
    pageSize
  });
};

// 创建新的活动类型
export const createActType = (label, remark, creator) => {
  return getRequest(`${baseUrl}/public/dictData/createNewType`, {
    label,
    remark: remark || '', // 如果remark为空，传空字符串
    creator
  });
};

// 更新活动类型
export const updateActType = (sort, label, remark) => {
  return getRequest(`${baseUrl}/public/dictData/updateActType`, {
    sort,
    label,
    remark: remark || '' // 如果remark为空，传空字符串
  });
};

// 删除活动类型
export const deleteActType = (sort) => {
  return getRequest(`${baseUrl}/public/dictData/removeActType`, {
    sort
  });
};
