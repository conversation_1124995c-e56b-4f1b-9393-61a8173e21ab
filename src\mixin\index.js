// import { uploadService } from '@/lib/api';
// import { errorCaptured } from '@/utils/index';
const maxSize = 20 * 1024 * 1024;
const imgFormatMixin = {
  methods: {
    // 上传之前
    beforeUploadHandle(file) {
      // 20M
      if (file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png') {
        this.$message.error('只支持jpg、png格式的图片！');
        return false;
      } else if (file.size > maxSize) {
        this.$message.error('超过最大上传限制！');
        return false;
      }
    }
    // onError(res) {
    //   if (res.status === 401) {
    //     this.$message({
    //       type: 'error',
    //       message: '登陆过期,请重新登陆',
    //       duration: 1000,
    //       onClose: () => {
    //         this.$store.commit('setAuthorization', '');
    //         this.$store.commit('initCachedViews');
    //         this.$router.push('/login');
    //       }
    //     });
    //   } else {
    //     this.$message.error('上传失败');
    //   }
    // }
  }
};



export {
  imgFormatMixin
  // richTextMixin
};
