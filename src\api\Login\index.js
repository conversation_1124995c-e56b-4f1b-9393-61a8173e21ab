import { postRequest, getRequest } from '../../assets/js/request';
import { baseUrl } from '../../config/constants';

// 登录
export const LoginPc = params => {
  return postRequest(`${baseUrl}/userLogin`, params);
};

// 退出登录
export const LogoutPc = () => {
  return getRequest(`${baseUrl}/userLogout`);
};

// 修改密码
export const ChangePwdPc = params => {
  return postRequest(`${baseUrl}/public/modifyPassword`, params);
};
