import Vue from 'vue';
import VueRouter from 'vue-router';
import Home from '../views/pc/Home.vue';
import Login from '../views/pc/Login.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/Home',
    name: 'Home',
    component: Home,
    // redirect: '/public',
    children: [
      {
        path: 'public',
        component: () => import('../views/pc/public/Index.vue'),
        meta: { title: '创建发布' }
      },
      {
        path: 'list',
        component: () => import('../views/pc/list/Index.vue'),
        meta: { title: '活动列表' }
      },
      {
        path: 'list/detail',
        component: () => import('../views/pc/detail/Index.vue'),
        meta: { title: '活动详情' }
      },
      {
        path: 'edit/:id',
        component: () => import('../views/pc/public/Index.vue'),
        meta: { title: '编辑活动信息' }
      },
      {
        path: 'type',
        component: () => import('../views/pc/type/Index.vue'),
        meta: { title: '活动类型管理' }
      }
    ]
  },
  {
    path: '/Login',
    name: 'Login',
    component: Login
  },
  {
    path: '/',
    redirect: '/Login'
  }
];

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes
});

// 未登录时无法进入活动页
router.beforeEach((to, from, next) => {
  const token = sessionStorage.getItem('userInfo');
  if (!token && to.name !== 'Login') {
    next('/Login');
  } else if (token && to.name === 'Login') {
    alert('如需登录其他账号，请退出当前帐号');
    next(false);
  }
  else {
    next();
  }
});

// 解决vue路由重复
const VueRouterPush = VueRouter.prototype.push;

VueRouter.prototype.push = function push(to) {
  return VueRouterPush.call(this, to).catch(err => err);
};

export default router;
