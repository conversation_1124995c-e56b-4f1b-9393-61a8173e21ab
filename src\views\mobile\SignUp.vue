<template>
  <div class="signup-container">
    <!-- 美化的导航栏 -->
    <div class="modern-nav-bar">
      <div class="nav-content">
        <div class="nav-left" @click="onBack">
          <van-icon name="arrow-left" size="18" />
          <span>返回</span>
        </div>
        <div class="nav-title">
          <h2>报名信息</h2>
          <p>Activity Registration</p>
        </div>
        <div class="nav-right">
          <!-- 可以添加其他操作按钮 -->
        </div>
      </div>
      <!-- 装饰性元素 -->
      <div class="nav-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
      </div>
    </div>

    <div class="content">
      <van-loading
          type="spinner"
          class="spinner"
          color="#2563eb"
          v-if="loading"
        />
      <FormCreate
        v-else
        :formFields="signUpFormList"
        :formFieldsValue="formFieldValue"
        :columns="captainList"
        :isDisabled='checked'
        :team="JSON.parse(this.$route.query.team)"
        :isTeam='Number(this.$route.query.isTeam)'
        :bringRelativesNum="this.$route.query.bringRelativesNum"
        @submit="emitSubmit">
        <template v-if="$route.query.agreement !==''">
        <van-checkbox class="footer" v-model="checked" shape="square" checked-color="#2563eb">是否同意
            <a :href="agreementUrl" @click="stopBubble" :download="agreementUrl">协议</a>
          </van-checkbox>
        </template>
      </FormCreate>
    </div>
  </div>
</template>

<script>
import FormCreate from '../../components/FormCreate';
import { getSignUpFieldFromMobile, signUpFromMobile } from '../../api/signUp/index';
import requestMixin from '../../mixin/requestMixin';
import { getTeamListFromMobile } from '../../api/team/index';
export default {
  components: {
    FormCreate
  },
  mixins: [requestMixin],
  data() {
    return {
      name: '',
      sex: '1',
      isFamily: false,
      checked: false,
      // 报名信息字段
      signUpFormList: [],
      captainList: [],
      formFieldValue: {},
      agreementUrl: '',
      loading: false,
      timer: null
    };
  },
  created() {
    if (!this.$route.query.actId && !this.$route.query.teamMax) {
      this.$router.push('/list');
    } else {
      this.$route.query.agreement === '' && (this.checked = true);
      this.$route.query.agreement !== '' && this.formatUrl(this.$route.query.agreement);
      this.getSignUpFieldFromMobile();
      this.getTeamListFromMobile();
    }
  },
  beforeDestroy() {
    if (this.timer) {
      this.timer = null;
    }
  },
  methods: {
    formatUrl(url) {
      const index = url.lastIndexOf('/');
      const fileName = url.slice(index + 1, url.length);
      this.agreementUrl = `api/file/public/download?filePath=${url}&fileName=${fileName}&from=mobile`;
    },
    emitSubmit(val, isTeamLeader) {
      const info = {};
      info.carrier = Number(val['亲属人数']);
      if (this.$route.query.isTeam === '0') {
        info.applyInfo = JSON.stringify(val);
        info.actId = Number(this.$route.query.actId);
      } else if (isTeamLeader === '1') {
        // 自由组队模式下并选择成为队长
        info.applyInfo = JSON.stringify(val);
        info.isTeamLeader = 1;
        info.actId = Number(this.$route.query.actId);
      } else {
        try {
          this.captainList.forEach(item => {
            if (item.teamName === val['队伍']) {
              info.applyInfo = JSON.stringify(val);
              info.isTeamLeader = 0;
              info.teamId = item.id;
              info.actId = Number(this.$route.query.actId);
              throw new Error('stop');
            }
          });
        } catch (e) {}
      }
      this.loading = true;
      signUpFromMobile(info).then(res => {
        const { message, status } = res.data;
        if (status === 0) {
          this.$toast.success('报名成功');
          this.$router.go(-1);
        } else if (status === -1 && message === '候补中') {
          this.$toast.fail(message);
          // this.timer = setTimeout(() => {
          this.$router.go(-1);
          // }, 1000);
        } else {
          this.$toast.fail(message);
        }
        this.loading = false;
      });
    },
    async getTeamListFromMobile() {
      const [err, res] = await this.request(getTeamListFromMobile, {
        actId: this.$route.query.actId,
        currentPage: -1,
        pageSize: 100
      });
      if (err) {
        return;
      }
      res.data.data.map(item => {
        item.disabled = item.number >= this.$route.query.teamMax;
        return item;
      });
      this.captainList = res.data.data;
    },
    async getSignUpFieldFromMobile() {
      const [err, res] = await this.request(getSignUpFieldFromMobile, {
        actId: this.$route.query.actId
      });
      if (err) {
        return;
      }
      // 格式[{id: xx, fieldName: "姓名", isRequired: 1, deleted: 0, actId: 35, isHide: 0},···]
      const signUpFormList = res.data.data;
      this.formFieldValue = this.getFormFieldValue();

      const { isTeam, team, isAllowedBring } = this.$route.query;

      for (let i = 0; i < signUpFormList.length; i++) {
        const item = signUpFormList[i];
        // 判断是否组队模式,不组队去掉队伍字段
        if (item.fieldName === '队伍') {
          if (Number(isTeam) === 0) {
            signUpFormList.splice(i, 1);
            i--;
          }
          if (team !== '{}') {
            this.formFieldValue[item.fieldName] = JSON.parse(team).teamName;
          }
        }

        // 判断是否允许携带亲属，不允许去掉携带亲属字段
        if (item.fieldName === '亲属人数' || item.fieldName === '亲属备注') {
          if (Number(isAllowedBring) === 0) {
            signUpFormList.splice(i, 1);
            i--;
          }
        }
      }

      this.signUpFormList = [...signUpFormList];
    },
    getFormFieldValue() {
      const value = {};
      this.signUpFormList.forEach((item, index) => {
        value[item.fieldName] = '';
      });
      value['姓名'] = localStorage.getItem('user');
      return value;
    },
    onBack() {
      this.$router.go(-1);
    },
    stopBubble(e) {
      e.stopPropagation();
    }
  }
};
</script>

<style lang="scss" scoped>
.signup-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 现代化导航栏样式 */
.modern-nav-bar {
  position: relative;
  background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
  color: white;
  padding: 20px 0 30px;
  box-shadow: 0 4px 20px rgba(37, 99, 235, 0.3);
  overflow: hidden;

  .nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;
    z-index: 2;

    .nav-left {
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px 12px;
      border-radius: 20px;
      position: relative;
      z-index: 10;
      user-select: none;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &:active {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(0.98);
      }

      .van-icon {
        margin-right: 6px;
        pointer-events: none;
      }

      span {
        font-size: 14px;
        font-weight: 500;
        pointer-events: none;
      }
    }

    .nav-title {
      text-align: center;
      flex: 1;

      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }

      p {
        margin: 4px 0 0;
        font-size: 12px;
        opacity: 0.8;
        font-weight: 300;
      }
    }

    .nav-right {
      width: 60px; /* 保持布局平衡 */
    }
  }

  /* 装饰性元素 */
  .nav-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);

      &.circle-1 {
        width: 120px;
        height: 120px;
        top: -60px;
        right: -30px;
        animation: float 6s ease-in-out infinite;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        bottom: -40px;
        left: -20px;
        animation: float 8s ease-in-out infinite reverse;
      }
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.content {
  padding: 20px;
  background: white;
  margin: 0 10px;
  border-radius: 20px 20px 0 0;
  margin-top: -10px;
  position: relative;
  z-index: 1;
  min-height: calc(100vh - 120px);

  .footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    text-align: center;
    font-size: 14px;
    margin-top: 20px;

    :deep(.van-button) {
      margin: 0 20px;
      height: 44px;
      padding: 0 30px;
      border-radius: 22px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37, 99, 235, 0.4);
      }
    }

    :deep(.van-checkbox) {
      justify-content: center;
      margin: 20px 0;

      .van-checkbox__label {
        color: #666;
        font-size: 14px;

        a {
          color: #2563eb;
          text-decoration: none;
          font-weight: 500;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

.spinner {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 9999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modern-nav-bar {
    padding: 15px 0 25px;

    .nav-content {
      padding: 0 15px;

      .nav-title h2 {
        font-size: 18px;
      }

      .nav-title p {
        font-size: 11px;
      }
    }
  }

  .content {
    margin: 0 5px;
    padding: 15px;
    border-radius: 15px 15px 0 0;

    .footer {
      padding: 15px;

      :deep(.van-button) {
        height: 40px;
        padding: 0 25px;
        margin: 0 15px;
      }
    }
  }
}
</style>
