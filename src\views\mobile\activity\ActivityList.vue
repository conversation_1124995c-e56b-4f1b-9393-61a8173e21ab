<template>
  <div class="activity-list-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">活动中心</h1>
        <p class="page-subtitle">发现精彩活动，参与美好时光</p>
      </div>
      <div class="header-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="tabs-container">
      <van-tabs
        v-model="active"
        animated
        swipeable
        @click-tab="onClick"
        @change="onClick"
        class="modern-tabs"
      >
        <van-tab
          v-for="tab in tabList"
          :title="tab.title"
          :key="tab.id"
          :name="tab.id"
        >
          <!-- 加载状态 -->
          <div v-if="tabLoading" class="loading-container">
            <van-loading
              type="spinner"
              color="#2563eb"
              size="24px"
              vertical
            >
              加载中...
            </van-loading>
          </div>

          <!-- 内容区域 -->
          <template v-else>
            <van-list
              v-if="activityList.length !== 0"
              v-model="loading"
              :finished="finished"
              @load="onLoad"
              finished-text="🎉 已经到底了"
              class="activity-list"
              :offset="10"
            >
              <activity-item
                v-for="(item, index) in activityList"
                :activity="item"
                :key="`${item.id}-${index}`"
                class="activity-item-wrapper"
                :style="{ animationDelay: `${index * 0.1}s` }"
              />
            </van-list>

            <!-- 空状态 -->
            <div v-else class="empty-state">
              <div class="empty-icon">🎪</div>
              <h3 class="empty-title">暂无活动</h3>
              <p class="empty-description">
                {{ getEmptyDescription() }}
              </p>
              <van-button
                type="primary"
                size="small"
                round
                @click="refreshData"
              >
                刷新试试
              </van-button>
            </div>
          </template>
        </van-tab>
      </van-tabs>
    </div>
  </div>
</template>

<script>
import ActivityItem from './components/ActivityItem';
import { getActivityListFromMobile } from '../../../api/activity/index';
import requestMixin from '../../../mixin/requestMixin';
import { convertTimeWithMinutes } from '../../../utils/index';
export default {
  components: {
    ActivityItem
  },
  mixins: [requestMixin],
  data() {
    return {
      tabList: [
        {
          title: '报名中',
          id: 1
        },
        {
          title: '我参与的',
          id: 2
        },
        {
          title: '未开始',
          id: 3
        },
        {
          title: '全部',
          id: 4
        }
      ],
      activityList: [],
      active: 1,
      loading: false,
      currentPage: 1,
      total: 0,
      pageNumber: 10,
      preActive: '0',
      tabLoading: false,
      isFirstLoad: true
    };
  },
  computed: {
    finished() {
      const num = this.currentPage * this.pageNumber - this.total;
      if (num >= 0) {
        return true;
      }
      return false;
    }
  },
  created() {
    this.getActivityListFromMobile(this.active, 1);
  },
  methods: {
    async getActivityListFromMobile(flag, currentPage) {
      const params = {
        createTime: convertTimeWithMinutes(new Date().getTime()),
        flag,
        pageSize: currentPage,
        pageNumber: this.pageNumber
      };
      const [err, res] = await this.request(getActivityListFromMobile, params);

      if (err) {
        return;
      }
      this.loading = false;
      // 连接数组
      if (this.activeChange) {
        this.preActive = flag;
        this.tabLoading = false;
        this.activityList = res ? res.data.data.records : [];
        this.activeChange = false;
      } else {
        // 解决onclick事件后发送onload获取相同的数据
        if (res && res.data.data.records.toString() !== this.activityList.toString()) {
          this.activityList = this.activityList.concat(...res.data.data.records);
        }
      }
      this.total = res ? res.data.data.total : 0;
    },
    onClick(name) {
      if (this.preActive === name) {
        this.activeChange = false;
      } else {
        this.activeChange = true;
        this.tabLoading = true;
        this.currentPage = 1;
      }
      this.getActivityListFromMobile(name, 1);
    },
    onLoad() {
      this.currentPage = this.currentPage + 1;
      this.getActivityListFromMobile(this.active, this.currentPage);
    },
    getEmptyDescription() {
      const descriptions = {
        1: '当前没有正在报名的活动，请稍后再来看看',
        2: '您还没有参与任何活动，快去报名参加吧',
        3: '暂时没有即将开始的活动',
        4: '系统中暂时没有任何活动'
      };
      return descriptions[this.active] || '暂无相关活动';
    },
    refreshData() {
      this.tabLoading = true;
      this.currentPage = 1;
      this.getActivityListFromMobile(this.active, 1);
    },

    // 初始化滚动监听，实现标签页固定效果
    initScrollListener() {
      this.$nextTick(() => {
        const tabsWrap = document.querySelector('.van-tabs__wrap');
        const tabsContainer = document.querySelector('.tabs-container');

        if (tabsWrap && tabsContainer) {
          let isFixed = false;
          let originalTop = 0;

          // 获取标签页容器的初始位置
          const getOriginalPosition = () => {
            const rect = tabsContainer.getBoundingClientRect();
            originalTop = rect.top + window.scrollY;
            console.log('标签页初始位置:', originalTop);
          };

          // 滚动事件处理
          const handleScroll = () => {
            const scrollTop = window.scrollY;
            console.log('当前滚动位置:', scrollTop, '标签页位置:', originalTop);

            if (scrollTop >= originalTop && !isFixed) {
              // 滚动到标签页位置，固定在顶部
              console.log('固定标签页');
              tabsWrap.style.position = 'fixed';
              tabsWrap.style.top = '0px';
              tabsWrap.style.left = '0px';
              tabsWrap.style.right = '0px';
              tabsWrap.style.zIndex = '1000';
              tabsWrap.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
              tabsWrap.classList.add('is-fixed');
              isFixed = true;
            } else if (scrollTop < originalTop && isFixed) {
              // 滚动回原位置，恢复正常定位
              console.log('恢复标签页');
              tabsWrap.style.position = '';
              tabsWrap.style.top = '';
              tabsWrap.style.left = '';
              tabsWrap.style.right = '';
              tabsWrap.style.zIndex = '';
              tabsWrap.style.boxShadow = '';
              tabsWrap.classList.remove('is-fixed');
              isFixed = false;
            }
          };

          // 初始化位置
          getOriginalPosition();

          // 监听滚动事件
          window.addEventListener('scroll', handleScroll, { passive: true });

          // 监听窗口大小变化，重新计算位置
          window.addEventListener('resize', getOriginalPosition);

          // 组件销毁时清理事件监听
          this.$once('hook:beforeDestroy', () => {
            window.removeEventListener('scroll', handleScroll);
            window.removeEventListener('resize', getOriginalPosition);
          });
        }
      });
    }
  },

  mounted() {
    this.getActivityListFromMobile(this.active, this.currentPage);
    this.initScrollListener();
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.activity-list-container {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  // 页面头部
  .page-header {
    position: relative;
    padding: px2rem(40px) px2rem(20px) px2rem(40px);
    color: #fff;
    overflow: hidden;

    .header-content {
      position: relative;
      z-index: 2;

      .page-title {
        font-size: px2rem(28px);
        font-weight: 700;
        margin: 0 0 px2rem(8px) 0;
        text-shadow: 0 px2rem(2px) px2rem(4px) rgba(0, 0, 0, 0.3);

        // PC端字体大小适配
        @media screen and (min-width: 768px) {
          font-size: px2rem(36px);
          margin: 0 0 px2rem(12px) 0;
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(42px);
          margin: 0 0 px2rem(16px) 0;
        }
      }

      .page-subtitle {
        font-size: px2rem(14px);
        opacity: 0.9;
        margin: 0;
        font-weight: 400;

        // PC端字体大小适配
        @media screen and (min-width: 768px) {
          font-size: px2rem(18px);
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(20px);
        }
      }
    }

    .header-decoration {
      position: absolute;
      top: 0;
      right: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;

      .decoration-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);

        &.circle-1 {
          width: px2rem(120px);
          height: px2rem(120px);
          top: px2rem(-60px);
          right: px2rem(-60px);
          animation: float 6s ease-in-out infinite;
        }

        &.circle-2 {
          width: px2rem(80px);
          height: px2rem(80px);
          top: px2rem(20px);
          right: px2rem(40px);
          animation: float 4s ease-in-out infinite reverse;
        }

        &.circle-3 {
          width: px2rem(60px);
          height: px2rem(60px);
          top: px2rem(80px);
          right: px2rem(120px);
          animation: float 5s ease-in-out infinite;
        }
      }
    }
  }

  // 标签页容器
  .tabs-container {
    background: #fff;
    border-radius: px2rem(20px) px2rem(20px) 0 0;
    min-height: calc(100vh - px2rem(180px));
    margin-top: px2rem(-20px);
    position: relative;
    z-index: 1;

    ::v-deep .modern-tabs {
      .van-tabs__wrap {
        background: #fff;
        padding: px2rem(15px) px2rem(15px) px2rem(10px) px2rem(15px);
        transition: all 0.3s ease;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        box-shadow: 0 px2rem(2px) px2rem(8px) rgba(0, 0, 0, 0.1);

        // 当固定时的增强样式
        &.is-fixed {
          box-shadow: 0 px2rem(4px) px2rem(16px) rgba(0, 0, 0, 0.2) !important;
          border-bottom: 1px solid rgba(0, 0, 0, 0.1);

          .van-tabs__nav {
            background: rgba($color-primary, 0.08);
          }
        }

        // PC端标签页容器适配
        @media screen and (min-width: 768px) {
          padding: px2rem(18px) px2rem(18px) px2rem(16px) px2rem(18px);
        }

        @media screen and (min-width: 1200px) {
          padding: px2rem(20px) px2rem(20px) px2rem(20px) px2rem(20px);
        }

        .van-tabs__nav {
          background: rgba($color-primary, 0.05);
          border-radius: px2rem(25px);
          padding: px2rem(4px);

          // PC端导航容器适配
          @media screen and (min-width: 768px) {
            border-radius: px2rem(30px);
            padding: px2rem(6px);
          }

          @media screen and (min-width: 1200px) {
            border-radius: px2rem(35px);
            padding: px2rem(8px);
          }

          .van-tab {
            border-radius: px2rem(20px);
            font-weight: 600;
            font-size: px2rem(14px);
            transition: all 0.3s ease;
            color: #666;
            min-height: px2rem(44px);

            // PC端标签字体大小适配 - 进一步增大
            @media screen and (min-width: 768px) {
              font-size: px2rem(20px);
              border-radius: px2rem(26px);
              min-height: px2rem(52px);
              padding: px2rem(14px) px2rem(24px);
            }

            @media screen and (min-width: 1200px) {
              font-size: px2rem(24px);
              border-radius: px2rem(32px);
              min-height: px2rem(64px);
              padding: px2rem(18px) px2rem(32px);
              font-weight: 700;
            }

            @media screen and (min-width: 1600px) {
              font-size: px2rem(28px);
              border-radius: px2rem(36px);
              min-height: px2rem(72px);
              padding: px2rem(22px) px2rem(40px);
              font-weight: 700;
            }

            &.van-tab--active {
              background: linear-gradient(135deg, $color-primary, $color-primary-light);
              color: #fff;
              box-shadow: 0 px2rem(4px) px2rem(12px) rgba($color-primary, 0.3);

              // PC端激活状态适配
              @media screen and (min-width: 768px) {
                box-shadow: 0 px2rem(6px) px2rem(16px) rgba($color-primary, 0.4);
                transform: translateY(px2rem(-2px));
              }

              @media screen and (min-width: 1200px) {
                box-shadow: 0 px2rem(8px) px2rem(20px) rgba($color-primary, 0.5);
                transform: translateY(px2rem(-3px));
              }
            }

            // PC端悬浮效果
            @media screen and (min-width: 768px) {
              &:hover:not(.van-tab--active) {
                background: rgba($color-primary, 0.1);
                color: $color-primary;
                transform: translateY(px2rem(-1px));
              }
            }
          }
        }

        .van-tabs__line {
          display: none;
        }
      }
    }
  }

  // 加载状态
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: px2rem(300px);

    // PC端加载容器适配
    @media screen and (min-width: 768px) {
      height: px2rem(400px);
    }

    @media screen and (min-width: 1200px) {
      height: px2rem(500px);
    }

    .van-loading {
      color: $color-primary;

      // PC端加载图标大小适配
      @media screen and (min-width: 768px) {
        font-size: px2rem(28px);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(32px);
      }

      ::v-deep .van-loading__text {
        font-size: px2rem(14px);

        @media screen and (min-width: 768px) {
          font-size: px2rem(16px);
        }

        @media screen and (min-width: 1200px) {
          font-size: px2rem(18px);
        }
      }
    }
  }

  // 活动列表 - 响应式网格布局
  .activity-list {
    padding: 0 px2rem(16px) px2rem(20px);

    // 使用CSS Grid实现响应式布局
    display: grid;
    gap: px2rem(16px);

    // 移动端：1列
    grid-template-columns: 1fr;

    // 大屏手机/小平板 (480px+): 2列
    @media screen and (min-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      padding: 0 px2rem(20px) px2rem(20px);
      gap: px2rem(20px);
    }

    // 平板 (768px+): 2-3列
    @media screen and (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      padding: 0 px2rem(24px) px2rem(24px);
      gap: px2rem(24px);
    }

    // 大平板/小屏PC (1024px+): 3列
    @media screen and (min-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      padding: 0 px2rem(32px) px2rem(32px);
      gap: px2rem(28px);
    }

    // 中屏PC (1200px+): 3-4列
    @media screen and (min-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
      gap: px2rem(32px);
    }

    // 大屏PC (1600px+): 4列
    @media screen and (min-width: 1600px) {
      grid-template-columns: repeat(4, 1fr);
    }

    // 超大屏 (2000px+): 5列
    @media screen and (min-width: 2000px) {
      grid-template-columns: repeat(5, 1fr);
    }

    .activity-item-wrapper {
      animation: slideInUp 0.6s ease-out;
      animation-fill-mode: both;
      margin-bottom: 0; // 移除底部边距，使用grid gap

      // 在网格布局中，最后一个元素不需要特殊处理
      &:last-child {
        margin-bottom: 0;
      }
    }

    ::v-deep .van-list__finished-text {
      color: #999;
      font-size: px2rem(12px);
      padding: px2rem(20px) 0;
      text-align: center;
      grid-column: 1 / -1; // 跨越所有列

      // PC端字体大小适配
      @media screen and (min-width: 768px) {
        font-size: px2rem(14px);
        padding: px2rem(24px) 0;
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(16px);
        padding: px2rem(28px) 0;
      }
    }

    ::v-deep .van-list__loading {
      padding: px2rem(20px) 0;
      grid-column: 1 / -1; // 跨越所有列

      @media screen and (min-width: 768px) {
        padding: px2rem(24px) 0;
      }

      @media screen and (min-width: 1200px) {
        padding: px2rem(28px) 0;
      }
    }
  }

  // 空状态
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: px2rem(80px) px2rem(40px);
    text-align: center;
    grid-column: 1 / -1; // 跨越所有列

    .empty-icon {
      font-size: px2rem(64px);
      margin-bottom: px2rem(20px);
      animation: bounce 2s infinite;

      // PC端图标大小适配
      @media screen and (min-width: 768px) {
        font-size: px2rem(80px);
        margin-bottom: px2rem(24px);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(96px);
        margin-bottom: px2rem(28px);
      }
    }

    .empty-title {
      font-size: px2rem(18px);
      font-weight: 600;
      color: #333;
      margin: 0 0 px2rem(8px) 0;

      // PC端字体大小适配
      @media screen and (min-width: 768px) {
        font-size: px2rem(24px);
        margin: 0 0 px2rem(12px) 0;
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(28px);
        margin: 0 0 px2rem(16px) 0;
      }
    }

    .empty-description {
      font-size: px2rem(14px);
      color: #666;
      line-height: 1.5;
      margin: 0 0 px2rem(24px) 0;
      max-width: px2rem(240px);

      // PC端字体大小适配
      @media screen and (min-width: 768px) {
        font-size: px2rem(16px);
        margin: 0 0 px2rem(28px) 0;
        max-width: px2rem(320px);
      }

      @media screen and (min-width: 1200px) {
        font-size: px2rem(18px);
        margin: 0 0 px2rem(32px) 0;
        max-width: px2rem(400px);
      }
    }
  }
}

// 动画定义
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(px2rem(-20px));
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(px2rem(30px));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(px2rem(-10px));
  }
  70% {
    transform: translateY(px2rem(-5px));
  }
  90% {
    transform: translateY(px2rem(-2px));
  }
}

// 响应式适配
@media screen and (max-width: 320px) {
  .activity-list-container {
    .page-header {
      padding: px2rem(50px) px2rem(16px) px2rem(30px);

      .header-content {
        .page-title {
          font-size: px2rem(24px);
        }

        .page-subtitle {
          font-size: px2rem(12px);
        }
      }
    }

    .tabs-container {
      min-height: calc(100vh - px2rem(160px));
    }
  }
}

// 保持小屏设备的原有适配
@media screen and (max-width: 320px) {
  .activity-list-container {
    .page-header {
      padding: px2rem(50px) px2rem(16px) px2rem(30px);

      .header-content {
        .page-title {
          font-size: px2rem(24px);
        }

        .page-subtitle {
          font-size: px2rem(12px);
        }
      }
    }

    .tabs-container {
      min-height: calc(100vh - px2rem(160px));
    }
  }
}

</style>
