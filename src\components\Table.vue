<template>
  <div class="modern-table-container">
    <!-- 表格头部工具栏 -->
    <div v-if="$slots.toolbar" class="table-toolbar">
      <slot name="toolbar"></slot>
    </div>

    <!-- 表格主体 -->
    <div class="table-wrapper" :class="tableWrapperClass">
      <table class="modern-table" :class="tableClass">
        <thead>
          <tr>
            <th
              v-for="item in thead"
              :key="'th' + item.prop"
              :class="getColumnClass(item)"
              :style="getColumnStyle(item)"
            >
              <div class="th-content">
                <span class="th-text">{{ item.label }}</span>
                <i v-if="item.sortable" class="el-icon-sort sort-icon"></i>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(tdObj, index) in data"
            :key="'td' + index"
            :class="getRowClass(tdObj, index)"
            @click="onClickTr(tdObj)"
          >
            <td
              v-for="(item, idx) in thead"
              :key="`tr${index}-td${idx}`"
              :class="getColumnClass(item)"
              :style="getColumnStyle(item)"
            >
              <div class="td-content">
                <slot v-if="item.slotName" :name="item.slotName" :data="tdObj" :index="index"></slot>
                <template v-else>
                  <span :class="getCellClass(item, tdObj)">
                    {{ formatCellValue(item, tdObj) }}
                  </span>
                </template>
              </div>
            </td>
          </tr>
          <!-- 空状态 -->
          <tr v-if="data.length === 0" class="empty-row">
            <td :colspan="thead.length" class="empty-cell">
              <div class="empty-content">
                <i class="el-icon-document empty-icon"></i>
                <p class="empty-text">{{ emptyText }}</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页器 -->
    <div v-if="showPagination" class="table-pagination">
      <van-pagination
        v-model="pageIndex"
        :total-items="totalPages"
        :items-per-page="perPage"
        @change="changePagination"
      >
        <template #prev-text>
          <van-icon name="arrow-left" />
        </template>
        <template #next-text>
          <van-icon name="arrow" />
        </template>
        <template #page="{text}">{{ text }}</template>
      </van-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernTable',
  props: {
    thead: {
      type: Array,
      default() {
        return [];
      }
    },
    data: {
      type: Array,
      default() {
        return [];
      }
    },
    isTeam: {
      type: Number,
      default: 0
    },
    border: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: true
    },
    hover: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium',
      validator: value => ['mini', 'small', 'medium', 'large'].includes(value)
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    currentPage: {
      type: Number,
      default: 1
    },
    totalPages: {
      type: Number,
      default: 0
    },
    perPage: {
      type: Number,
      default: 10
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageIndex: this.currentPage
    };
  },
  computed: {
    tableWrapperClass() {
      return {
        'table-loading': this.loading
      }
    },
    tableClass() {
      return {
        'table-border': this.border,
        'table-stripe': this.stripe,
        'table-hover': this.hover,
        [`table-${this.size}`]: this.size
      }
    }
  },
  methods: {
    onClickTr(obj) {
      this.$emit('on-click-tr', obj);
    },
    changePagination(val) {
      this.pageIndex = val;
      this.$emit('page-size-change', val);
    },
    getColumnClass(column) {
      return {
        'text-left': column.align === 'left',
        'text-center': column.align === 'center' || !column.align,
        'text-right': column.align === 'right',
        sortable: column.sortable
      };
    },
    getColumnStyle(column) {
      const style = {};
      if (column.width) {
        style.width = typeof column.width === 'number' ? `${column.width}px` : column.width;
      }
      if (column.minWidth) {
        style.minWidth = typeof column.minWidth === 'number' ? `${column.minWidth}px` : column.minWidth;
      }
      return style;
    },
    getRowClass(row, index) {
      return {
        'row-clickable': this.$listeners['on-click-tr']
      };
    },
    getCellClass(column, row) {
      const classes = [];

      // 根据列类型添加样式
      if (column.type === 'status') {
        classes.push('status-cell');
        if (row[column.prop]) {
          classes.push(`status-${row[column.prop].toLowerCase()}`);
        }
      }

      return classes;
    },
    formatCellValue(column, row) {
      const value = row[column.prop];

      // 特殊格式化逻辑
      if (column.prop === 'teamName' && this.isTeam === 3) {
        return `${value}(${row.teamLeaderName})`;
      }

      // 日期格式化
      if (column.type === 'date' && value) {
        return this.formatDate(value);
      }

      // 数字格式化
      if (column.type === 'number' && typeof value === 'number') {
        return value.toLocaleString();
      }

      return value || '-';
    },
    formatDate(date) {
      if (!date) return '-';
      const d = new Date(date);
      return d.toLocaleDateString('zh-CN');
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.modern-table-container {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;

  // 表格工具栏
  .table-toolbar {
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafbfc;
  }

  // 表格包装器
  .table-wrapper {
    position: relative;
    overflow-x: auto;

    &.table-loading {
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 10;
      }
    }
  }

  // 现代化表格样式
  .modern-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 14px;
    color: #606266;
    background-color: #fff;

    // 表头样式
    thead {
      background-color: #fafbfc;

      th {
        padding: 12px 16px;
        font-weight: 600;
        color: #303133;
        border-bottom: 1px solid #ebeef5;
        white-space: nowrap;
        user-select: none;

        .th-content {
          display: flex;
          align-items: center;
          justify-content: center;

          .th-text {
            flex: 1;
          }

          .sort-icon {
            margin-left: 4px;
            color: #c0c4cc;
            cursor: pointer;
            transition: color 0.3s;

            &:hover {
              color: $color-primary;
            }
          }
        }

        &.text-left .th-content {
          justify-content: flex-start;
        }

        &.text-right .th-content {
          justify-content: flex-end;
        }

        &.sortable {
          cursor: pointer;

          &:hover {
            background-color: #f0f2f5;
          }
        }
      }
    }

    // 表体样式
    tbody {
      tr {
        transition: background-color 0.3s;

        &.row-clickable {
          cursor: pointer;
        }

        td {
          padding: 12px 16px;
          border-bottom: 1px solid #ebeef5;
          vertical-align: middle;

          .td-content {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 20px;

            .status-cell {
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 12px;
              font-weight: 500;

              &.status-active {
                background-color: rgba($color-success, 0.1);
                color: $color-success;
              }

              &.status-inactive {
                background-color: rgba($color-error, 0.1);
                color: $color-error;
              }

              &.status-pending {
                background-color: rgba($color-warning, 0.1);
                color: $color-warning;
              }
            }
          }

          &.text-left .td-content {
            justify-content: flex-start;
          }

          &.text-right .td-content {
            justify-content: flex-end;
          }
        }

        &:last-child td {
          border-bottom: none;
        }
      }

      // 空状态行
      .empty-row {
        .empty-cell {
          padding: 40px 20px;
          text-align: center;
          border-bottom: none;

          .empty-content {
            .empty-icon {
              font-size: 48px;
              color: #c0c4cc;
              margin-bottom: 16px;
            }

            .empty-text {
              color: #909399;
              font-size: 14px;
              margin: 0;
            }
          }
        }
      }
    }

    // 表格尺寸变体
    &.table-mini {
      font-size: 12px;

      th, td {
        padding: 8px 12px;
      }
    }

    &.table-small {
      font-size: 13px;

      th, td {
        padding: 10px 14px;
      }
    }

    &.table-large {
      font-size: 15px;

      th, td {
        padding: 16px 20px;
      }
    }

    // 边框变体
    &.table-border {
      border: 1px solid #ebeef5;

      th, td {
        border-right: 1px solid #ebeef5;

        &:last-child {
          border-right: none;
        }
      }
    }

    // 斑马纹变体
    &.table-stripe {
      tbody tr:nth-child(even) {
        background-color: #fafbfc;
      }
    }

    // 悬停效果变体
    &.table-hover {
      tbody tr:hover {
        background-color: #f5f7fa;
      }
    }
  }

  // 分页器
  .table-pagination {
    padding: 16px 20px;
    border-top: 1px solid #ebeef5;
    background-color: #fafbfc;
    display: flex;
    justify-content: center;
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .modern-table-container {
    .table-wrapper {
      .modern-table {
        font-size: 12px;

        th, td {
          padding: 8px 12px;

          .th-content,
          .td-content {
            font-size: 12px;
          }
        }

        .empty-row .empty-cell {
          padding: 24px 16px;

          .empty-content {
            .empty-icon {
              font-size: 32px;
              margin-bottom: 12px;
            }

            .empty-text {
              font-size: 12px;
            }
          }
        }
      }
    }

    .table-toolbar,
    .table-pagination {
      padding: 12px 16px;
    }
  }
}

// 移动端表格滚动提示
@media screen and (max-width: 768px) {
  .table-wrapper {
    position: relative;

    &::after {
      content: '← 左右滑动查看更多 →';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.6);
      color: #fff;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      transition: opacity 0.3s;
      pointer-events: none;
      z-index: 5;
    }

    &:hover::after {
      opacity: 1;
    }
  }
}
</style>
