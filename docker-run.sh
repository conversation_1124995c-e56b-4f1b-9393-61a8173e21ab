#!/bin/sh

#脚本调试，有详细的日志输出
set -x

#告诉bash 任何语句的执行结果不是true的时候就应该退出。防止滚雪球的错误
set -e

#环境变量替换
# envsubst '$xxx 指定某个/某些需要替换的环境变量' <源文件,里面包含了$xxx的环境变量> xxxx替换掉环境变量的文件
envsubst '$RESOURCE_URL' < /etc/nginx/conf.d/default.template > /etc/nginx/nginx.conf
cat /etc/nginx/nginx.conf

# docker 容器启动的时候，会默认把容器里面第一个进程（pid = 1 ）的程序作为判断容器是否在运行的依据。 而nginx默认是在后台运行的。
# docker 还没执行到自定义的cmd前，nginx的pid 为1 执行到cmd后，nginx在后台运行，所以一旦执行完自定义的cmd后，如果nginx在后台运行的话
# nginx 容器会自动退出。所以为了不让容器退出，nginx 要让在前台运行
nginx -g "daemon off;"
exit
EOF
