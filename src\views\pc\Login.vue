<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="login-content">
      <!-- 左侧信息区域 -->
      <div class="info-section">
        <div class="brand-logo">
          <div class="logo-icon">
            <i class="el-icon-s-platform"></i>
          </div>
          <h1 class="brand-title">九联服务号</h1>
          <p class="brand-subtitle">活动发布管理平台</p>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <i class="el-icon-check"></i>
            <span>高效的活动管理</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-check"></i>
            <span>实时数据统计</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-check"></i>
            <span>安全可靠的服务</span>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-section">
        <div class="login-card">
          <div class="card-header">
            <h2 class="login-title">欢迎登录</h2>
            <p class="login-subtitle">请输入您的账号信息</p>
          </div>

          <el-form class="login-form" :rules="rules" :model="loginForm" ref="loginForm">
            <el-form-item prop="account" class="form-item">
              <div class="input-wrapper">
                <i class="el-icon-user input-icon"></i>
                <el-input
                  type="text"
                  class="custom-input"
                  clearable
                  placeholder="请输入手机号"
                  v-model="loginForm.account"/>
              </div>
            </el-form-item>

            <el-form-item prop="password" class="form-item">
              <div class="input-wrapper">
                <i class="el-icon-lock input-icon"></i>
                <el-input
                  type="password"
                  class="custom-input"
                  placeholder="请输入密码"
                  v-model="loginForm.password"
                  show-password/>
              </div>
            </el-form-item>

            <el-button
              class="login-btn"
              type="primary"
              @click="submitLogin('loginForm')"
              :loading="loading">
              {{ loading ? '登录中...' : '登录' }}
            </el-button>
          </el-form>

          <div class="login-footer">
            <p class="copyright">© 2024 九联服务号. All rights reserved.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { LoginPc } from '../../api/Login/index';
export default {
  name: 'Login',
  data() {
    const validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('密码不能为空'));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      loginForm: {
        account: '',
        password: ''
      },
      rules: {
        account: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { pattern: /^1[345789]\d{9}$/, message: '账号格式错误', trigger: 'blur' }
        ],
        password: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    submitLogin(forName) {
      this.$refs[forName].validate((valid) => {
        if (valid) {
          this.loading = true;
          LoginPc({
            username: this.loginForm.account,
            password: this.loginForm.password
          }).then((res) => {
            this.loading = false;
            if (res.data && res.data.message === '成功') {
              this.$message({
                type: 'success',
                message: '登录成功'
              });
              const userInfo = res.data.data.user.name;
              window.sessionStorage.setItem('userInfo', userInfo);
              this.$router.push('/Home/public');
            } else {
              this.$message({
                type: 'error',
                message: res.data.message || '登录失败'
              });
            }
          }).catch(() => {
            this.loading = false;
            this.$message({
              type: 'error',
              message: '登录失败，请稍后重试'
            });
          });
        } else {
          this.$message({
            type: 'error',
            message: '格式错误，请重新填写'
          });
        }
      });
    }
  }
};
</script>

<style scoped>
/* 主容器 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 10%;
  right: 20%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 主要内容区域 */
.login-content {
  display: flex;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
  z-index: 2;
  min-height: 600px;
}

/* 左侧信息区域 */
.info-section {
  flex: 1;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: white;
  position: relative;
}

.info-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.brand-logo {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-icon i {
  font-size: 36px;
  color: white;
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(45deg, #fff, #e0e7ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.feature-list {
  position: relative;
  z-index: 1;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  opacity: 0.9;
}

.feature-item i {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 12px;
}

/* 右侧登录区域 */
.login-section {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-card {
  width: 100%;
  max-width: 400px;
}

.card-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-title {
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 10px 0;
}

.login-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 表单样式 */
.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: 24px;
}

.form-item:deep(.el-form-item__content) {
  line-height: normal;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  color: #9ca3af;
  font-size: 16px;
  z-index: 10;
}

.custom-input:deep(.el-input__inner) {
  height: 52px;
  padding-left: 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.custom-input:deep(.el-input__inner):focus {
  border-color: #2563eb;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.custom-input:deep(.el-input__inner):hover {
  border-color: #d1d5db;
  background: #fff;
}

.custom-input:deep(.el-input__suffix) {
  right: 16px;
}

.custom-input:deep(.el-input__suffix-inner) {
  color: #9ca3af;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  margin-top: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-btn:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.login-btn:hover:before {
  left: 100%;
}

/* 页脚 */
.login-footer {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.copyright {
  font-size: 12px;
  color: #9ca3af;
  margin: 0;
}

/* 错误提示样式 */
.form-item:deep(.el-form-item__error) {
  color: #ef4444;
  font-size: 12px;
  margin-top: 6px;
  padding-left: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    margin: 20px;
    min-height: auto;
  }

  .info-section {
    padding: 40px 30px;
    text-align: center;
  }

  .brand-title {
    font-size: 24px;
  }

  .login-section {
    padding: 40px 30px;
  }

  .login-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .login-content {
    margin: 10px;
  }

  .info-section,
  .login-section {
    padding: 30px 20px;
  }

  .brand-title {
    font-size: 20px;
  }

  .login-title {
    font-size: 20px;
  }

  .custom-input:deep(.el-input__inner) {
    height: 48px;
    font-size: 14px;
  }

  .login-btn {
    height: 48px;
    font-size: 14px;
  }
}

/* 加载状态 */
.login-btn.is-loading {
  pointer-events: none;
}

.login-btn.is-loading:before {
  display: none;
}
</style>
