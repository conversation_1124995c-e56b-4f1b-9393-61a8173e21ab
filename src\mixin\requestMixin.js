// 后台返回的请求成功的自定义码
const SUCCESS_CODE = 0;

// 需要处理的错误的 http 状态码
const ERR_STATUS = {
  500: '服务器错误，请联系网络管理员寻求帮助',
  404: '未找到资源',
  400: '请求错误',
  401: '认证失效，请重新登录',
  403: '您无权执行此操作',
  CONNECTION_FAILURE: '网络连接错误，请联系网络管理员寻求帮助'
};

// 取消 401 重复提醒
let tipLock = false;

// 判断fn是否为函数/promise，是返回true，否返回false
const isFunction = fn => (!fn ? false : typeof fn === 'function');
const isPromise = p => (!p ? false : typeof p.then === 'function');

// 根据返回的 data 验证请求是否成功
const validateRequestSuccess = data => {
  const { code } = data;
  return code === SUCCESS_CODE;
};

function fail(vm, response, errCb) {
  // const status = (response && response.status) || 'CONNECTION_FAILURE';

  const status = response && response.data.status;
  const isErrCbString = typeof errCb === 'string';
  if (status && ERR_STATUS[status]) {
    !tipLock && vm.$message && vm.$message.warning(ERR_STATUS[status]);
    if (status === 401) {
      vm.$router.push('/Login');
      tipLock = true;
      return;
    }

    // 发生状态码错误时只执行回调函数，字符串不提示
    errCb && !isErrCbString && errCb.call(this, status, response.data);
    return;
  }

  if (errCb) {
    isErrCbString
      ? vm.$message && vm.$message.warning(errCb)
      : errCb.call(this, status, response.data);
  }

  // 状态码成功，code不为0，提示
  if (
    response
    && response.data.code
    && response.data.code !== 0
    && response.data.code !== -1
  ) {
    vm.$message
      && vm.$message({
        type: 'warning',
        message: response.data.message,
        duration: 2000,
        showClose: true
      });
  }
}

/**
 * 代理 api 请求，返回真实数据，而不是 axios 封装过的数据
 *
 * @param {function | promise} requestFn 请求函数或者 promise
 * @param {any} params 请求函数的参数
 * @param {function | string} errCb 发生错误时的回调函数或者提示信息
 * @returns {array} [data | null, err | null] 当没有发生错误时只有 data，否则只有 err: { status , data }
 */
export async function request(requestFn, params, errCb) {
  if (!isFunction(requestFn) && !isPromise(requestFn)) {
    throw new Error('the first param must be function or promise');
  }

  const promise = isFunction(requestFn) ? requestFn(params) : requestFn;

  try {
    const res = await promise;
    if (Array.isArray(res)) {
      const realData = res.map(result => {
        return result.data;
      });
      return [realData, null];
    }

    if (res && validateRequestSuccess(res.data)) {
      tipLock = false;
      return [res.data, null];
    }

    fail(this, res, errCb);
    return res ? (
      ERR_STATUS[res.status]
      ? [null, null]
      : [null, { status: res.status, data: res.data }]
    ) : [null, null];
  } catch (error) {
    console.error(error);

    // axios 将错误的 response 挂载在 error 对象上
    const response = error.response || null;
    const status = response && response.status;
    const data = response && response.data;
    const message = response && response.message;
    fail(this, response, (data && data.message) || errCb);
    return ERR_STATUS[status]
      ? [null, null]
      : [null, { status, data, message }];
  }
}

export default {
  methods: { request }
};
