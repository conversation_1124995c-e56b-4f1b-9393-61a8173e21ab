<template>
   <el-submenu
   v-if="menu.children && menu.children.length"
   :index="menu.route"
   :popper-append-to-body="false"
   >
    <template slot="title">
        <i v-if="menu.icon" :class="menu.icon"></i>
        <span>{{menu.menuName}}</span>
    </template>
    <sub-menu
        v-for="item in menu.children"
        :key="item.menuId"
        :menu="item"
    />
  </el-submenu>
  <el-menu-item
   v-else
    :index="menu.route"
    :route="menu.route"
  >
    <i v-if="menu.icon" :class="menu.icon"></i>
    <span slot="title">{{menu.menuName}}</span>
  </el-menu-item>
</template>

<script>
import SubMenu from './SubMenu.vue';
export default {
  name: 'sub-menu',
  props: {
    menu: {
      type: Object,
      required: true
    }

  },
  components: {
    SubMenu
  },
  computed: {}

};

</script>

<style lang="scss" scoped>

</style>
