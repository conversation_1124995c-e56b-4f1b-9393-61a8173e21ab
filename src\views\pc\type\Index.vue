<template>
  <div class="activity-type-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h2 class="page-title">活动类型管理</h2>
        <p class="page-description">管理系统中的活动类型，支持增加、删除、修改和查询操作</p>
      </div>
      <div class="header-actions">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="showAddDialog"
          size="medium"
        >
          新增类型
        </el-button>
        <el-button
          icon="el-icon-refresh"
          @click="refreshData"
          size="medium"
        >
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索活动类型名称..."
        prefix-icon="el-icon-search"
        @input="handleSearch"
        clearable
        style="width: 300px;"
      />
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="filteredTypeList"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      >
        <el-table-column
          type="index"
          label="序号"
          width="80"
          align="center"
          :index="getTableIndex"
        />
        <el-table-column
          prop="dictLabel"
          label="类型"
          min-width="200"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getTagType(scope.$index)"
              size="medium"
            >
              {{ scope.row.dictLabel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          label="备注"
          min-width="150"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            <span v-else style="color: #c0c4cc;">-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="180"
          align="center"
          :formatter="formatDateTime"
        />
        <el-table-column
          prop="createBy"
          label="创建者"
          width="120"
          align="center"
        />
        <el-table-column
          label="操作"
          width="200"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="primary"
              icon="el-icon-edit"
              size="mini"
              @click="showEditDialog(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="typeForm"
        label-width="100px"
      >
        <el-form-item label="类型名称" prop="label">
          <el-input
            v-model="form.label"
            placeholder="请输入活动类型名称"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入活动类型备注（可选）"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getActTypeTable,
  createActType,
  updateActType,
  deleteActType
} from '@/api/Type/index';

export default {
  name: 'ActivityTypeManagement',
  data() {
    return {
      // 数据列表
      typeList: [],
      filteredTypeList: [],
      loading: false,
      
      // 搜索
      searchKeyword: '',
      
      // 分页
      currentPage: 1,
      pageSize: 20,
      total: 0,
      
      // 对话框
      dialogVisible: false,
      dialogTitle: '新增活动类型',
      isEdit: false,
      submitting: false,
      
      // 表单
      form: {
        label: '',
        remark: ''
      },

      // 表单验证规则
      rules: {
        label: [
          { required: true, message: '请输入活动类型名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        remark: [
          { max: 200, message: '备注不能超过200个字符', trigger: 'blur' }
        ]
      }
    };
  },
  
  created() {
    this.loadData();
  },
  
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 调用新的分页API获取活动类型表格数据
        const response = await getActTypeTable(this.currentPage, this.pageSize);

        console.log('API响应数据:', response); // 调试日志

        // 检查响应是否成功 (status: 200 表示成功)
        if (response && response.status === 200 && response.data) {
          let typeData = [];
          const responseData = response.data.data;
          // 处理分页数据结构
          if (responseData.records && Array.isArray(responseData.records)) {
            typeData = responseData.records;
            this.total = responseData.total || 0;
            this.currentPage = responseData.current || 1;
            this.pageSize = responseData.size || 20;
          } else if (Array.isArray(responseData)) {
            // 如果data直接是数组
            typeData = responseData;
            this.total = typeData.length;
          }

          // 映射数据字段以匹配表格显示
          this.typeList = typeData.map(item => ({
            id: item.id,
            dictLabel: item.label,        // 映射label到dictLabel
            label: item.label,            // 保留原字段
            createTime: item.createTime,
            createBy: item.creator,       // 映射creator到createBy
            creator: item.creator,        // 保留原字段
            sort: item.sort,
            value: item.value,
            dictType: item.dictType,
            remark: item.remark
          }));

          // 如果没有分页信息，设置总数为数组长度
          if (!this.total) {
            this.total = this.typeList.length;
          }

          console.log('处理后的数据:', this.typeList); // 调试日志
          this.filterData();
        } else {
          // 处理API返回错误的情况
          const errorMsg = response?.message || '获取数据失败';
          console.error('获取活动类型失败:', errorMsg, response);
          this.generateMockData();
          this.filterData();
          this.$message.error(errorMsg);
        }
      } catch (error) {
        console.error('加载数据异常:', error);
        // 异常时使用模拟数据
        this.generateMockData();
        this.filterData();
        this.$message.error('加载数据失败，已切换到演示模式');
      } finally {
        this.loading = false;
      }
    },
    
    // 模拟API调用延迟
    simulateApiCall() {
      return new Promise(resolve => {
        setTimeout(resolve, 500);
      });
    },
    
    // 生成模拟数据
    generateMockData() {
      this.typeList = [
        {
          sort: 0,
          label: '徒步',
          description: '户外徒步活动，包括登山、健走等',
          createTime: '2024-01-15 10:30:00'
        },
        {
          sort: 1,
          label: '自定义',
          description: '用户自定义的活动类型',
          createTime: '2024-01-16 14:20:00'
        },
        {
          sort: 2,
          label: '学术讲座',
          description: '各类学术性质的讲座活动，包括专家报告、学术研讨等',
          createTime: '2024-01-17 09:15:00'
        },
        {
          sort: 3,
          label: '文体活动',
          description: '文化体育类活动，如运动会、文艺演出、比赛等',
          createTime: '2024-01-18 16:45:00'
        },
        {
          sort: 4,
          label: '技能培训',
          description: '各类技能培训活动，如职业技能、软件培训等',
          createTime: '2024-01-19 11:30:00'
        }
      ];
      this.total = this.typeList.length;
    },
    
    // 搜索处理
    handleSearch() {
      this.filterData();
    },
    
    // 过滤数据
    filterData() {
      if (!this.searchKeyword) {
        this.filteredTypeList = [...this.typeList];
      } else {
        this.filteredTypeList = this.typeList.filter(item =>
          (item.label && item.label.toLowerCase().includes(this.searchKeyword.toLowerCase())) ||
          (item.description && item.description.toLowerCase().includes(this.searchKeyword.toLowerCase()))
        );
      }
    },
    
    // 获取标签类型
    getTagType(sort) {
      const types = ['success', 'info', 'warning', 'danger', ''];
      return types[sort % types.length] || 'info';
    },
    

    
    // 显示新增对话框
    showAddDialog() {
      this.dialogTitle = '新增活动类型';
      this.isEdit = false;
      this.dialogVisible = true;
    },
    
    // 显示编辑对话框
    showEditDialog(row) {
      this.dialogTitle = '编辑活动类型';
      this.isEdit = true;
      // 只复制需要的字段
      this.form = {
        sort: row.sort,
        label: row.dictLabel, // 注意：后端字段是dictLabel，表单字段是label
        remark: row.remark || ''
      };
      this.dialogVisible = true;
    },
    
    // 删除处理
    handleDelete(row) {
      this.$confirm(`确定要删除活动类型"${row.dictLabel}"吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          // 调用API删除，传递sort参数
          const result = await deleteActType(row.sort);

          // 处理API响应
          if (result && result.status === 200) {
            // 删除成功，重新加载数据
            this.$message.success('删除成功');
            this.loadData(); // 重新加载数据以获取最新列表
          } else {
            const errorMsg = result?.message || '删除失败';
            this.$message.error(errorMsg);
          }
        } catch (error) {
          console.error('删除失败:', error);
          this.$message.error('删除失败');
        }
      });
    },
    
    // 提交表单
    handleSubmit() {
      this.$refs.typeForm.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          try {
            let result;
            if (this.isEdit) {
              // 调用更新API，传递sort, label, remark参数
              result = await updateActType(this.form.sort, this.form.label, this.form.remark);
            } else {
              // 调用创建API，传递label, remark, creator参数
              const creator = this.getCurrentUser();
              result = await createActType(this.form.label, this.form.remark, creator);
            }

            // 处理API响应
            if (result && result.status === 200) {
              if (this.isEdit) {
                // 更新成功，重新加载数据以获取最新列表
                this.$message.success('更新成功');
                this.loadData(); // 重新加载数据
              } else {
                // 创建成功，重新加载数据以获取最新列表
                this.$message.success('创建成功');
                this.loadData(); // 重新加载数据
              }

              this.filterData();
              this.dialogVisible = false;
            } else {
              const errorMsg = result?.message || (this.isEdit ? '更新失败' : '创建失败');
              this.$message.error(errorMsg);
            }
          } catch (error) {
            console.error('提交失败:', error);
            this.$message.error(this.isEdit ? '更新失败' : '创建失败');
          } finally {
            this.submitting = false;
          }
        }
      });
    },
    
    // 重置表单
    resetForm() {
      this.form = {
        label: '',
        remark: ''
      };
      if (this.$refs.typeForm) {
        this.$refs.typeForm.resetFields();
      }
    },
    
    // 刷新数据
    refreshData() {
      this.searchKeyword = '';
      this.currentPage = 1;
      this.loadData();
    },
    
    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val;
      this.currentPage = 1;
      this.loadData();
    },

    handleCurrentChange(val) {
      this.currentPage = val;
      this.loadData();
    },

    // 获取表格序号
    getTableIndex(index) {
      return (this.currentPage - 1) * this.pageSize + index + 1;
    },

    // 格式化日期时间
    formatDateTime(_row, _column, cellValue) {
      if (!cellValue) return '-';
      const date = new Date(cellValue);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    // 获取当前用户信息
    getCurrentUser() {
      // 优先从sessionStorage获取（PC登录）
      const sessionUser = window.sessionStorage.getItem('userInfo');
      if (sessionUser) {
        return sessionUser;
      }

      // 其次从localStorage获取（微信授权）
      const localUser = window.localStorage.getItem('user');
      if (localUser) {
        return localUser;
      }

      // 默认值
      return 'admin';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.activity-type-management {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 80px);

  // 页面头部
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .header-content {
      .page-title {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }

      .page-description {
        font-size: 14px;
        color: #909399;
        margin: 0;
        line-height: 1.5;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }

  // 搜索区域
  .search-section {
    margin-bottom: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  // 表格区域
  .table-section {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  // 分页区域
  .pagination-section {
    margin-top: 16px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    text-align: right;
  }
}

// 对话框样式优化
::v-deep .el-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, $color-primary, $color-primary-light);
    color: #fff;
    padding: 20px 24px;

    .el-dialog__title {
      color: #fff;
      font-weight: 600;
    }

    .el-dialog__close {
      color: #fff;
      font-size: 18px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
  }

  .dialog-footer {
    text-align: right;
    padding-top: 16px;
    border-top: 1px solid #ebeef5;
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .activity-type-management {
    padding: 16px;

    .page-header {
      flex-direction: column;
      gap: 16px;

      .header-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .search-section {
      padding: 12px 16px;

      .el-input {
        width: 100% !important;
      }
    }
  }
}
</style>
