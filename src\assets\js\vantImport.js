/*
 * @Author: your name
 * @Date: 2020-10-30 10:59:36
 * @LastEditTime: 2020-11-30 16:47:59
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /signing_up/src/assets/js/vantImport.js
 */
import Vue from 'vue';
import {
  Button,
  Icon,
  Image,
  Col,
  Row,
  Form,
  Radio,
  RadioGroup,
  Checkbox,
  Field,
  Tag,
  NavBar,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  Empty,
  Divider,
  GoodsAction,
  GoodsActionIcon,
  GoodsActionButton,
  Dialog,
  Picker,
  Popup,
  CellGroup,
  Cell,
  Toast,
  Skeleton,
  Pagination,
  List,
  Loading,
  Progress 
} from 'vant';

const components = {
  Button,
  Icon,
  Image,
  Col,
  Row,
  Form,
  Field,
  Radio,
  RadioGroup,
  Checkbox,
  Tag,
  NavBar,
  Tab,
  Tabs,
  Tabbar,
  TabbarItem,
  Empty,
  Divider,
  GoodsAction,
  GoodsActionIcon,
  GoodsActionButton,
  Dialog,
  Picker,
  Popup,
  CellGroup,
  Cell,
  Toast,
  Skeleton,
  Pagination,
  List,
  Loading,
  Progress 
};

for (const key in components) {
  Vue.use(components[key]);
}
