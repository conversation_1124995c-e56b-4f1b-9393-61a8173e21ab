<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>500</title>
    <style>
        .site-wrapper.site-page--not-found {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            overflow: hidden;
        }

        .site-content__wrapper {
            padding: 0;
            margin: 0;
            background-color: #fff;
        }

        .site-content {
            position: fixed;
            top: 15%;
            left: 50%;
            z-index: 2;
            padding: 30px;
            text-align: center;
            transform: translate(-50%, 0);
        }

        .not-found-title {
            margin: 20px 0 15px;
            font-size: 10em;
            font-weight: 400;
            color: rgb(55, 71, 79);
        }

        .not-found-desc {
            margin: 0 0 30px;
            font-size: 26px;
            text-transform: uppercase;
            color: rgb(118, 131, 143);
        }

        em {
            font-style: normal;
            color: #ee8145;
        }

        .not-found-btn-gohome {
            margin-left: 30px;
            background: #b4b4f5;
            color: #fff;
        }
        button {
            display: inline-block;
            line-height: 1;
            white-space: nowrap;
            cursor: pointer;
            background: #fff;
            border: 1px solid #dcdfe6;
            color: #606266;
            -webkit-appearance: none;
            text-align: center;
            box-sizing: border-box;
            outline: 0;
            margin: 0;
            transition: .1s;
            padding: 12px 20px;
            font-size: 14px;
            border-radius: 4px;
        }
    </style>
</head>

<body>
    <div class="site-wrapper site-page--not-found">
        <div class="site-content__wrapper">
            <div class="site-content">
                <h2 class="not-found-title">500</h2>
                <p class="not-found-desc">抱歉！您访问的页面<em>崩溃了</em>...</p>
                <button onclick="back()">返回上一页</button>
                <!-- <button onclick="home()" class="not-found-btn-gohome">进入首页</button> -->
            </div>
        </div>
    </div>
</body>
<script>    
    function back() {
        window.history.go(-1);
    }
    // function home() {
    //     window.location.href = '/';
    // }
</script>
</html>