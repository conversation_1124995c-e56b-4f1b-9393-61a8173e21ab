<template>
  <div class="form">
    <el-form ref="signUpForm">
      <!-- 根据前面写的是否携带亲属，是否组队判断报名信息的携带亲属与组队信息的是否显示 -->
      <el-form-item
      v-for="(item, index) in signUpFormList"
      :key="index"
      v-show="(isAllowedBring && !(item.fieldName.search('亲属') === -1))
      || ( isTeam && !(item.fieldName.search('队') === -1))
      || (item.fieldName.search('亲属')=== -1 && item.fieldName.search('队')=== -1)">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-input disabled :placeholder="item.fieldName" />
          </el-col>
          <el-col :span="2">
            <el-row>
              <el-col>
                <el-checkbox v-model="item.must">必填</el-checkbox>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="2" v-show="index < 5 ? false : true">
            <el-row>
              <el-col>
                <i class="el-icon-delete" @click="deleteField(index)"></i>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </el-form-item>
      <!-- <el-form-item>
        <el-button type="primary" @click="addField" size="small">
          <i class="el-icon-plus"></i> 添加字段
        </el-button>
      </el-form-item> -->
    </el-form>
    <el-dialog
      :title="addFieldTitle"
      v-if="dialogFormVisible"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      @close="cancel"
      width="500px"
    >
      <el-form :model="newField" :rules="rules" ref="form">
        <el-form-item prop="fieldName">
          <el-row :gutter="20">
            <el-col :span="4"> 字段名称 </el-col>
            <el-col :span="8">
              <el-select
                v-model="newField.fieldName"
                filterable
                allow-create
                default-first-option
                placeholder="请输入或选择">
                <el-option
                  v-for="item in fieldNameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"/>
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="是否必填">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-radio-group v-model="newField.must">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Title from '../../../components/title';
export default {
  components: {
    Title
  },
  props: {
    signUpFormList: {
      type: Array,
      default() {
        return [];
      }
    },
    isAllowedBring: {
      type: Number,
      default: 0
    },
    isTeam: {
      type: Number,
      default: 0
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      newField: { fieldName: '', isRequest: 1, must: true },
      dialogFormVisible: false,
      addFieldTitle: '添加字段',
      fieldNameOptions: [{
        value: '上车地点(市区、公司)',
        label: '上车地点(市区、公司)'
      }, {
        value: '部门',
        label: '部门'
      }, {
        value: '备注',
        label: '备注'
      }]
    };
  },
  methods: {
    addField() {
      this.dialogFormVisible = true;
      this.newField = { fieldName: '', isRequest: 1, must: true, deleted: 0 };
    },
    deleteField(index) {
      this.$emit('deleteSignUpField', index);
    },
    cancel() {
      this.dialogFormVisible = false;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.dialogFormVisible = false;
          this.newField.key = `${new Date().getTime()}`;
          this.newField.isRequest = this.newField.must === true ? 1 : 0;
          this.$emit('addSignUpField', this.newField);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.signup-form-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;

  .el-form {
    .el-form-item {
      margin-bottom: 20px;

      .el-form-item__label {
        font-weight: 500;
        color: #333;
      }

      .field-row {
        display: flex;
        align-items: center;
        gap: 20px;

        .field-input {
          flex: 1;
        }

        .field-controls {
          display: flex;
          align-items: center;
          gap: 15px;

          .delete-btn {
            cursor: pointer;
            color: #f56c6c;
            font-size: 16px;
            transition: all 0.3s ease;

            &:hover {
              color: #f78989;
              transform: scale(1.1);
            }
          }
        }
      }

      .add-field-section {
        text-align: center;
        margin-top: 20px;

        .el-button {
          padding: 10px 20px;
          border-radius: 6px;
        }
      }
    }
  }
}

// 弹窗样式
::v-deep .signup-field-dialog {
  .el-dialog {
    max-width: 450px;
  }

  .el-dialog__body {
    padding: 20px;
  }
}
</style>
