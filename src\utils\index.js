/**
 * 对函数进行防抖处理
 *
 * @param func 函数
 * @param wait 延迟执行毫秒数
 * @param immediate true 表立即执行，false 表非立即执行
 * @param errCallback 发生错误（）
 */
export const debounce = function(func, wait, immediate, errCallback) {
  let timeout;
  return function() {
    const context = this;
    /* eslint-disable */
    // 获取参数
    const args = arguments;

    // 如果在计时器运行时执行函数，重置计时器
    if (timeout) { clearTimeout(timeout); }

    // const callNow = !timeout;
    if (immediate) {
      const callNow = !timeout;
      timeout = setTimeout(() => {
        timeout = null;
      }, wait);
      if (callNow) {
        return func.apply(context, args);
      }
    } else {
      timeout = setTimeout(function() {
        return func.apply(context, args);
      }, wait);
    }

    if (timeout && typeof errCallback === 'function') {
      errCallback.apply(context);
    }
  };
};

/**
 * async/await 错误捕获处理
 * 
 * @param asyncFunc 异步方法
 */
export const errorCaptured = async function (asyncFunc) {
  try {
    const res = await asyncFunc;
    return [null, res];
  } catch (e) {
    return [e, null];
  }
};

export const convertTime = function (timestamp) {
  const time = new Date(Number(timestamp));
  const fullYear = time.getFullYear();
  // 个位数是在前面补0
  const month = (time.getMonth() + 1).toString().padStart(2, '0');
  const day = time.getDate().toString().padStart(2, '0');
  return `${fullYear}-${month}-${day}`;
}

export const convertTimeWithMinutes = function (timestamp) {
  const day = convertTime(timestamp);
  const time = new Date(Number(timestamp));
  const hour = time.getHours().toString().padStart(2, '0');
  const minutes = time.getMinutes().toString().padStart(2, '0');
  const second = time.getSeconds().toString().padStart(2, '0');
  return `${day} ${hour}:${minutes}:${second}`;
}