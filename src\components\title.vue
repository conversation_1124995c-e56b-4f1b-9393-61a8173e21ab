<template>
  <div class="modern-title" :style="{marginTop: top + 'px'}">
    <div class="title-indicator" :style="{backgroundColor: lineConfig.color || '#3c57ec'}"></div>
    <div class="title-content">
      <h3 class="title-text" :style="{fontSize: titleConfig.fontSize + 'px'}">
        {{ titleConfig.title }}
      </h3>
      <p v-if="titleConfig.subtitle" class="title-subtitle">
        {{ titleConfig.subtitle }}
      </p>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    lineConfig: {
      type: Object,
      default() {
        return {
          border: '5px solid #0BB2D4'
        };
      }
    },
    titleConfig: {
      type: Object,
      require: true
    },
    top: {
      type: Number,
      default() {
        return 20;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.modern-title {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;

  .title-indicator {
    width: 4px;
    height: 24px;
    border-radius: 2px;
    margin-right: 12px;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .title-content {
    flex: 1;

    .title-text {
      margin: 0;
      font-weight: 600;
      color: #303133;
      line-height: 1.4;
    }

    .title-subtitle {
      margin: 4px 0 0 0;
      font-size: 14px;
      color: #909399;
      line-height: 1.4;
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .modern-title {
    .title-indicator {
      height: 20px;
      margin-right: 8px;
    }

    .title-content {
      .title-text {
        font-size: 16px !important;
      }

      .title-subtitle {
        font-size: 12px;
      }
    }
  }
}
</style>
