<template>
  <div class="modern-activity-info">
    <!-- 活动主要信息区域 -->
    <div class="activity-main-section">
      <!-- 活动图片 -->
      <div class="activity-image-container">
        <div class="image-wrapper">
          <el-image
            class="activity-image"
            :src="'/image/'+activity.actImg"
            fit="cover"
            :preview-src-list="['/image/'+activity.actImg]"
            :z-index="9999"
            @load="onImageLoad"
            @error="onImageError"
          >
            <div slot="placeholder" class="image-placeholder">
              <i class="el-icon-picture-outline"></i>
              <span>加载中...</span>
            </div>
            <div slot="error" class="image-error">
              <i class="el-icon-picture-outline"></i>
              <span>加载失败</span>
              <el-button
                type="text"
                size="mini"
                @click.stop="retryLoadImage"
                class="retry-btn"
              >
                重试
              </el-button>
            </div>
          </el-image>
          <div class="image-overlay" @click="previewImage">
            <div class="overlay-content">
              <i class="el-icon-view"></i>
              <span>查看大图</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 活动详细信息 -->
      <div class="activity-details">
        <div class="activity-title-section">
          <div class="title-wrapper">
            <h1 class="activity-title">{{ activity.actName }}</h1>
            <!-- 活动类型标签 -->
            <span
              v-if="activity.actType !== undefined"
              class="activity-type-tag"
              :style="getTypeStyle(activity.actType)"
            >
              {{ getTypeText(activity.actType) }}
            </span>
          </div>
          <div class="activity-status-badge">
            <span
              v-if="activity.deleted === 2"
              class="status-badge status-cancelled"
            >
              <i class="el-icon-close"></i>
              已作废
            </span>
            <template v-else>
              <span
                v-if="activity.status === 1"
                class="status-badge status-not-start"
              >
                <i class="el-icon-time"></i>
                未开始
              </span>
              <span
                v-if="activity.status === 2"
                class="status-badge status-signup"
              >
                <i class="el-icon-user"></i>
                报名中
              </span>
              <span
                v-if="activity.status === 3"
                class="status-badge status-signup-end"
              >
                <i class="el-icon-warning"></i>
                报名截止
              </span>
              <span
                v-if="activity.status === 4"
                class="status-badge status-ongoing"
              >
                <i class="el-icon-video-play"></i>
                进行中
              </span>
              <span
                v-if="activity.status === 5"
                class="status-badge status-finished"
              >
                <i class="el-icon-circle-check"></i>
                已结束
              </span>
            </template>
          </div>
        </div>

        <!-- 活动信息网格 -->
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="info-content">
              <div class="info-label">报名时间</div>
              <div class="info-value">
                {{ activity.enlistStartTime }} - {{ activity.enlistEndTime }}
              </div>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon">
              <i class="el-icon-money"></i>
            </div>
            <div class="info-content">
              <div class="info-label">报名费用</div>
              <div class="info-value">¥{{ activity.enlistCost || 0 }}</div>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="info-content">
              <div class="info-label">活动人数</div>
              <div class="info-value">{{ activity.actNum }}人</div>
            </div>
          </div>

          <div class="info-item">
            <div class="info-icon">
              <i class="el-icon-date"></i>
            </div>
            <div class="info-content">
              <div class="info-label">活动时间</div>
              <div class="info-value">
                {{ activity.actStartTime }} - {{ activity.actEndTime }}
              </div>
            </div>
          </div>

          <div class="info-item full-width">
            <div class="info-icon">
              <i class="el-icon-location"></i>
            </div>
            <div class="info-content">
              <div class="info-label">活动地址</div>
              <div class="info-value">{{ activity.address }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section">
      <div class="action-buttons">
        <el-button
          type="primary"
          icon="el-icon-edit"
          class="action-btn primary-btn"
          @click="editActivity(activity.status)"
        >
          {{ (activity.status === 1 || activity.status === 2) ? '编辑活动' : '查看详情' }}
        </el-button>

        <el-button
          v-if="activity.deleted === 0"
          type="warning"
          icon="el-icon-delete"
          class="action-btn warning-btn"
          @click="activityControl(2)"
        >
          作废活动
        </el-button>

        <template v-else>
          <el-button
            type="success"
            icon="el-icon-switch-button"
            class="action-btn success-btn"
            @click="activityControl(0)"
          >
            启用活动
          </el-button>

          <el-button
            type="danger"
            icon="el-icon-delete"
            class="action-btn danger-btn"
            @click="activityControl(1)"
          >
            删除活动
          </el-button>
        </template>

        <el-button
          type="info"
          icon="el-icon-download"
          class="action-btn info-btn"
          @click="exportSignUpExcel"
        >
          导出名单
        </el-button>

        <el-button
          v-if="activity.activityDetail && activity.activityDetail.isLuckDraw === 1"
          :type="activity.activityDetail.isDraw === 0 ? 'primary' : 'info'"
          :icon="activity.activityDetail.isDraw === 0 ? 'el-icon-trophy' : 'el-icon-circle-check'"
          :class="['action-btn', 'lottery-btn', { 'shine-animation': activity.activityDetail.isDraw === 0 }]"
          :disabled="activity.activityDetail.isDraw === 1"
          @click="open"
        >
          {{ activity.activityDetail.isDraw === 0 ? '开始抽奖' : '已开奖' }}
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { exportSignUpExcel } from '@/api/signUp/index';
import { getAllActType } from '@/api/Type';
import stringToColor from 'string-to-color';

export default {

  props: {
    activity: {
      type: Object,
      require: true
    },
    signUpTotal: {
      type: Number
    },
    priceTotal: {
      type: Number
    }
  },
  data() {
    return {
      AllActType: [], // 存储活动类型数据
      imageLoadError: false // 图片加载错误状态
    };
  },
  async created() {
    try {
      const response = await getAllActType();
      this.AllActType = response.data.data;
    } catch (error) {
      console.error("获取活动类型失败：", error);
    }
  },
  // TODO 出现 call undefined 错误
  // watch: {
  //   activity: {
  //     immediate: true,
  //     deep: true
  //   }
  // },

  methods: {
    // 获取活动类型文本
    getTypeText(actType) {
      if (actType === undefined) return '';

      // 从API数据中获取标签文本
      const matchedItem = this.AllActType.find(type => type.sort === actType);
      return matchedItem ? matchedItem.label : '';
    },

    // 获取活动类型样式（基于string-to-color）
    getTypeStyle(actType) {
      if (actType === undefined) return {};

      const typeLabel = this.getTypeText(actType);
      if (!typeLabel) return {};

      // 使用string-to-color生成颜色
      const baseColor = stringToColor(typeLabel);

      return {
        color: baseColor,
        backgroundColor: `${baseColor}20`, // 20% 透明度背景
        borderColor: baseColor,
        border: `1px solid ${baseColor}`
      };
    },

    editActivity() {
      // if (status === 1) {
      //   this.$emit('editActivity');
      //   return;
      // }
      // this.$message.error('当前活动状态已不可编辑');
      this.$emit('editActivity');
    },
    activityControl(index) {
      this.$emit('activityControlFromPc', index);
    },
    open() {
      this.$emit('drawFromPc');
    },

    async exportSignUpExcel() {
      try {
        const { id, actName, fields } = this.activity;
        const params = {
          actId: id,
          actName,
          fields: fields ? fields.split(',').join('，') : ''
        };
        const res = await exportSignUpExcel(params);
        const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
        const objectUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = objectUrl;
        a.download = `${actName}活动-报名名单`;
        a.click();
        window.URL.revokeObjectURL(objectUrl);
      } catch (err) {
        this.$message.error('导出失败，请稍后重试');
      }
    },

    // 预览图片
    previewImage() {
      if (!this.activity.actImg) {
        this.$message.warning('暂无图片可预览');
        return;
      }
      // 手动触发 el-image 的预览功能
      const imageElement = this.$el.querySelector('.activity-image');
      if (imageElement && imageElement.__vue__) {
        // 直接调用 el-image 的预览方法
        imageElement.__vue__.clickHandler();
      } else {
        // 备用方案：使用 Element UI 的图片预览
        this.$imagePreview(['/image/' + this.activity.actImg]);
      }
    },

    // 图片加载成功
    onImageLoad() {
      this.imageLoadError = false;
      this.$emit('image-loaded');
    },

    // 图片加载失败
    onImageError() {
      this.imageLoadError = true;
      this.$emit('image-error');
    },

    // 重试加载图片
    retryLoadImage() {
      this.imageLoadError = false;
      // 强制重新加载图片
      this.$nextTick(() => {
        const imageElement = this.$el.querySelector('.activity-image img');
        if (imageElement) {
          const src = imageElement.src;
          imageElement.src = '';
          setTimeout(() => {
            imageElement.src = src + '?t=' + Date.now();
          }, 100);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.modern-activity-info {
  padding: 24px;

  // 活动主要信息区域
  .activity-main-section {
    display: flex;
    gap: 32px;
    margin-bottom: 32px;

    // 活动图片容器
    .activity-image-container {
      flex-shrink: 0;

      .image-wrapper {
        position: relative;
        width: 280px;
        height: 200px;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.18);

          .image-overlay {
            opacity: 1;
          }
        }

        .activity-image {
          width: 100%;
          height: 100%;
          border-radius: 16px;

          ::v-deep img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .image-placeholder,
        .image-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
          color: #909399;
          font-size: 24px;
          text-align: center;

          i {
            font-size: 32px;
            margin-bottom: 8px;
            opacity: 0.6;
          }

          span {
            font-size: 14px;
            margin-bottom: 8px;
          }

          .retry-btn {
            color: #409EFF;
            font-size: 12px;
            padding: 4px 8px;

            &:hover {
              background: rgba(64, 158, 255, 0.1);
              border-radius: 4px;
            }
          }
        }

        .image-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: all 0.3s ease;
          border-radius: 16px;

          .overlay-content {
            color: white;
            text-align: center;

            i {
              font-size: 24px;
              margin-bottom: 8px;
              display: block;
            }

            span {
              font-size: 14px;
              font-weight: 500;
            }
          }
        }
      }
    }

    // 活动详细信息
    .activity-details {
      flex: 1;

      .activity-title-section {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 24px;

        .title-wrapper {
          flex: 1;
          margin-right: 20px;

          .activity-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 12px 0;
            line-height: 1.3;
            letter-spacing: -0.5px;
          }

          .activity-type-tag {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            &.type-hiking {
              background: linear-gradient(135deg, #00b894, #00cec9);
              color: white;

              &::before {
                content: '🥾';
                margin-right: 6px;
                font-size: 16px;
              }
            }

            &.type-custom {
              background: linear-gradient(135deg, #6c5ce7, #a29bfe);
              color: white;

              &::before {
                content: '🎯';
                margin-right: 6px;
                font-size: 16px;
              }
            }
          }
        }

        .activity-status-badge {
          flex-shrink: 0;
        }
      }

      // 信息网格
      .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;

        .info-item {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px;
          background: rgba(255, 255, 255, 0.8);
          border-radius: 12px;
          border: 1px solid rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 1);
            border-color: rgba(64, 158, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          }

          &.full-width {
            grid-column: 1 / -1;
          }

          .info-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #409EFF, #36a3f7);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          .info-content {
            flex: 1;

            .info-label {
              font-size: 12px;
              color: #909399;
              margin-bottom: 4px;
              font-weight: 500;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }

            .info-value {
              font-size: 14px;
              color: #2c3e50;
              font-weight: 600;
              line-height: 1.4;
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  // 状态徽章样式
  .status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    i {
      font-size: 14px;
    }

    &.status-not-start {
      background: linear-gradient(135deg, #909399, #787c82);
      color: white;
    }

    &.status-signup {
      background: linear-gradient(135deg, #67C23A, #5daf34);
      color: white;
      animation: pulse 2s infinite;
    }

    &.status-signup-end {
      background: linear-gradient(135deg, #E6A23C, #cf9236);
      color: white;
    }

    &.status-ongoing {
      background: linear-gradient(135deg, #F56C6C, #f04747);
      color: white;
      animation: pulse 2s infinite;
    }

    &.status-finished {
      background: linear-gradient(135deg, #909399, #787c82);
      color: white;
    }

    &.status-cancelled {
      background: linear-gradient(135deg, #F56C6C, #f04747);
      color: white;
    }
  }

  // 操作按钮区域
  .action-section {
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding-top: 24px;

    .action-buttons {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;

      .action-btn {
        border-radius: 10px;
        padding: 12px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        &.primary-btn {
          background: linear-gradient(135deg, #409EFF, #36a3f7);

          &:hover {
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
          }
        }

        &.warning-btn {
          background: linear-gradient(135deg, #E6A23C, #cf9236);

          &:hover {
            box-shadow: 0 6px 20px rgba(230, 162, 60, 0.4);
          }
        }

        &.success-btn {
          background: linear-gradient(135deg, #67C23A, #5daf34);

          &:hover {
            box-shadow: 0 6px 20px rgba(103, 194, 58, 0.4);
          }
        }

        &.danger-btn {
          background: linear-gradient(135deg, #F56C6C, #f04747);

          &:hover {
            box-shadow: 0 6px 20px rgba(245, 108, 108, 0.4);
          }
        }

        &.info-btn {
          background: linear-gradient(135deg, #909399, #787c82);

          &:hover {
            box-shadow: 0 6px 20px rgba(144, 147, 153, 0.4);
          }
        }

        &.lottery-btn {
          background: linear-gradient(135deg, #E91E63, #AD1457);

          &:hover {
            box-shadow: 0 6px 20px rgba(233, 30, 99, 0.4);
          }

          &.shine-animation {
            animation: shine 2s infinite;
          }

          &:disabled {
            background: linear-gradient(135deg, #909399, #787c82);
            transform: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    transform: scale(1.02);
  }
  100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

@keyframes shine {
  0% {
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
  }
  50% {
    box-shadow: 0 8px 24px rgba(233, 30, 99, 0.6);
    transform: translateY(-2px) scale(1.02);
  }
  100% {
    box-shadow: 0 4px 12px rgba(233, 30, 99, 0.3);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-activity-info {
    padding: 16px;

    .activity-main-section {
      flex-direction: column;
      gap: 20px;

      .activity-image-container {
        align-self: center;

        .image-wrapper {
          width: 100%;
          max-width: 280px;
          height: 180px;
        }
      }

      .activity-details {
        .activity-title-section {
          flex-direction: column;
          gap: 16px;

          .activity-title {
            margin-right: 0;
          }
        }

        .info-grid {
          grid-template-columns: 1fr;
          gap: 16px;

          .info-item.full-width {
            grid-column: 1;
          }
        }
      }
    }

    .action-section {
      .action-buttons {
        justify-content: center;

        .action-btn {
          flex: 1;
          min-width: 120px;
        }
      }
    }
  }
}
</style>
