<template>
  <div class="modern-slide-menu">
    <!-- 菜单头部 -->
    <div class="menu-header">
      <div class="logo-section">
        <div class="logo-icon">
          <i class="el-icon-s-grid"></i>
        </div>
        <div class="logo-text">
          <h3>活动管理</h3>
          <p>Activity Management</p>
        </div>
      </div>
    </div>

    <!-- 菜单内容 -->
    <div class="menu-content">
      <el-menu
        :style="{height: menuHeight}"
        :mode="mode"
        :default-active="defaultActive"
        :unique-opened="uniqueOpened"
        :text-color="textColor"
        :background-color="backgroundColor"
        @select="handleSelect"
        router
        class="modern-menu"
      >
        <sub-menu
          v-for="menu in menuList"
          :key="menu.menuId"
          :menu="menu"
        />
      </el-menu>
    </div>

    <!-- 菜单底部 - 用户账号区域 -->
    <div class="menu-footer">
      <div class="user-section">
        <!-- 用户信息和下拉菜单 -->
        <div class="user-dropdown">
          <el-dropdown trigger="click" @command="handleCommand" placement="top-end">
            <div class="user-info">
              <img class="user-avatar" src="@/assets/images/avator.gif" alt="user-avatar">
              <div class="user-details">
                <span class="user-name">管理员</span>
                <span class="user-role">Administrator</span>
              </div>
              <i class="el-icon-more dropdown-icon"></i>
            </div>
            <el-dropdown-menu slot="dropdown" class="user-dropdown-menu">
              <el-dropdown-item command="profile" class="dropdown-item">
                <i class="el-icon-user"></i>
                <span>个人资料</span>
              </el-dropdown-item>
              <el-dropdown-item command="sync" class="dropdown-item">
                <i class="el-icon-refresh"></i>
                <span>同步通讯录</span>
              </el-dropdown-item>
              <el-dropdown-item command="changePass" class="dropdown-item">
                <i class="el-icon-key"></i>
                <span>修改密码</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout" class="dropdown-item logout">
                <i class="el-icon-switch-button"></i>
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 修改密码弹窗 -->
    <el-dialog
      title="修改密码"
      width="30%"
      :visible.sync="dialogTableVisible"
      class="pwd-dialog"
      :close-on-click-modal="false"
    >
      <el-form
        :model="pwdFormData"
        :rules="rules"
        ref="pwdForm"
        label-width="70px"
      >
        <el-form-item label="新密码:" prop="newPwd">
          <el-input
            type="password"
            size="small"
            v-model="pwdFormData.newPwd"
            maxlength="16"
            spellcheck="false"
            clearable
            show-password
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="info" size="small" @click="dialogTableVisible=false">取 消</el-button>
          <el-button type="primary" size="small" @click="submitForm('pwdForm')">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import SubMenu from './SubMenu.vue';
import { syncAddressList } from '../../api/activity/index';
import { LogoutPc, ChangePwdPc } from '../../api/Login/index';

export default {
  props: {
    // 菜单列表
    menuList: {
      type: Array,
      requried: true
    },
    // 菜单模式
    mode: {
      type: String,
      default() {
        return 'vertical';
      }
    },

    // 默认选中的菜单下标
    defaultActive: {
      type: String,
      require: true
    },

    // 是否唯一展开子菜单
    uniqueOpened: {
      type: Boolean,
      default() {
        return false;
      }
    },

    // 默认文字的颜色
    textColor: {
      type: String,
      default() {
        return '';
      }
    },

    // 选中的文字的颜色
    activeTextColor: {
      type: String,
      default() {
        return '#2563eb'; // 主题色
      }
    },

    // 菜单背景色
    backgroundColor: {
      type: String,
      default() {
        return 'transparent'; // 透明背景，继承父组件的渐变背景
      }
    }

  },

  components: {
    SubMenu
  },
  data() {
    return {
      // 修改密码弹窗相关数据
      dialogTableVisible: false,
      pwdFormData: {
        newPwd: ''
      },
      rules: {
        newPwd: [{
          required: true,
          min: 6,
          max: 16,
          message: '长度在6到16个字符',
          trigger: 'blur'
        }]
      }
    };
  },
  methods: {
    handleSelect() {
      // this.$emit('menu-select', index);
    },

    // 处理下拉菜单命令
    handleCommand(command) {
      if (command === 'sync') {
        this.syncAddressList();
      } else if (command === 'logout') {
        this.logout();
      } else if (command === 'changePass') {
        this.dialogTableVisible = true;
      } else if (command === 'profile') {
        this.$message.info('个人资料功能开发中...');
      }
    },

    // 同步通讯录
    syncAddressList() {
      syncAddressList().then(res => {
        if (res.data.status === 0) {
          this.$message.success('通讯录同步成功');
        } else {
          this.$message.error('通讯录同步失败');
        }
      }).catch(() => {
        this.$message.error('通讯录同步失败');
      });
    },

    // 提交密码修改
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          ChangePwdPc({
            newPassword: this.pwdFormData.newPwd
          }).then(res => {
            if (res.data.status === 200) {
              this.$message.success('密码修改成功，需重新登录');
              this.dialogTableVisible = false;
              this.pwdFormData.newPwd = '';
              // 修改密码成功后退出登录
              this.logout();
            } else {
              this.$message.error(res.data.message || '密码修改失败');
            }
          }).catch((error) => {
            console.error('修改密码失败:', error);
            this.$message.error('密码修改失败，请稍后重试');
          });
        } else {
          this.$message.error('请检查输入格式');
        }
      });
    },

    // 退出登录
    logout() {
      this.$confirm('确定要退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        LogoutPc().then((res) => {
          if (res.data.status === 200) {
            this.$message.success('退出登录成功');
            window.sessionStorage.removeItem('userInfo');
            this.$router.push('/Login');
          } else {
            this.$message.error('退出登录失败');
          }
        }).catch(() => {
          this.$message.error('退出登录失败');
        });
      });
    }
  },
  computed: {
    // 自动高度,高度是对于页面高度减去头部高度60
    autoHeight() {
      return `calc(100vh - ${60}px)`;
    },
    menuHeight() {
      return 'calc(100vh - 160px)'; // 减去头部和底部的高度
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.modern-slide-menu {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;

  // 菜单头部
  .menu-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .logo-section {
      display: flex;
      align-items: center;
      color: #fff;

      .logo-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
        backdrop-filter: blur(10px);

        i {
          font-size: 24px;
          color: #fff;
        }
      }

      .logo-text {
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        p {
          margin: 2px 0 0 0;
          font-size: 12px;
          opacity: 0.8;
          font-weight: 300;
        }
      }
    }
  }

  // 菜单内容
  .menu-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;
    
    .modern-menu.el-menu {
      border: none;
      background: transparent !important; // 强制透明背景，继承父组件渐变

      // 使用更高特异性的选择器来覆盖Element UI默认样式
      &.el-menu .el-menu-item,
      &.el-menu .el-submenu__title {
        color: rgba(255, 255, 255, 0.9);
        border-radius: 8px;
        margin: 4px 16px;
        padding: 0 20px;
        height: 48px;
        line-height: 48px;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s ease;
        vertical-align: baseline;
        background-color: transparent;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
          transform: translateX(4px);
        }

        &.is-active {
          background: rgba(255, 255, 255, 0.2);
          color: #fff;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: #fff;
            border-radius: 2px;
          }
        }

        i {
          margin-right: 12px;
          font-size: 18px;
        }
      }

      // 使用更高特异性的选择器来覆盖子菜单样式
      &.el-menu .el-submenu {
        .el-submenu__title {
          position: relative;

          .el-submenu__icon-arrow {
            color: rgba(255, 255, 255, 0.7);
            transition: transform 0.3s ease;
          }

          &:hover .el-submenu__icon-arrow {
            color: #fff;
          }
        }

        &.is-opened .el-submenu__title .el-submenu__icon-arrow {
          transform: rotateZ(180deg);
        }

        .el-menu.el-menu--inline {

          .el-menu-item {
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            padding-left: 60px;
            font-size: 14px;
            font-weight: 400;

            &:hover {
              background: rgba(255, 255, 255, 0.1);
              color: #fff;
            }

            &.is-active {
              background: rgba(255, 255, 255, 0.15);
              color: #fff;
            }
          }
        }
      }
    }
  }

  // 菜单底部 - 用户区域
  .menu-footer {
    padding: 16px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: auto;
    background: rgba(0, 0, 0, 0.1);

    .user-section {
      display: flex;
      flex-direction: column;

      // 用户下拉菜单
      .user-dropdown {
        position: relative;

        .user-info {
          display: flex;
          align-items: center;
          color: #fff;
          cursor: pointer;
          padding: 8px 12px;
          border-radius: 12px;
          background: rgba(255, 255, 255, 0.1);
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            margin-right: 12px;
            border: 2px solid rgba(255, 255, 255, 0.3);
          }

          .user-details {
            flex: 1;

            .user-name {
              display: block;
              font-size: 14px;
              font-weight: 600;
              margin-bottom: 2px;
              line-height: 1.2;
            }

            .user-role {
              display: block;
              font-size: 11px;
              opacity: 0.8;
              line-height: 1.2;
            }
          }

          .dropdown-icon {
            font-size: 12px;
            opacity: 0.8;
            transition: transform 0.3s ease;
          }

          &:hover .dropdown-icon {
            transform: rotate(90deg);
          }
        }


      }
    }
  }

  // 下拉菜单样式
  ::v-deep .user-dropdown-menu {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: none;
    padding: 8px 0;
    min-width: 180px;

    .dropdown-item {
      display: flex;
      align-items: center;
      padding: 12px 20px;
      color: #333;
      transition: all 0.3s ease;

      i {
        margin-right: 12px;
        font-size: 16px;
        width: 16px;
      }

      &:hover {
        background: #f8f9ff;
        color: $color-primary;
      }

      &.logout {
        color: #ff4757;

        &:hover {
          background: #fff5f5;
          color: #ff4757;
        }
      }
    }
  }

  // 密码修改弹窗样式
  ::v-deep .pwd-dialog {
    .el-dialog {
      border-radius: 12px;
    }

    .el-dialog__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      padding: 20px 24px;
      border-radius: 12px 12px 0 0;

      .el-dialog__title {
        color: #fff;
        font-weight: 600;
      }

      .el-dialog__close {
        color: #fff;
      }
    }

    .el-dialog__body {
      padding: 24px;
    }

    .el-dialog__footer {
      padding: 16px 24px;
      border-top: 1px solid #eee;
    }
  }

  // 滚动条样式
  .menu-content::-webkit-scrollbar {
    width: 4px;
  }

  .menu-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  .menu-content::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .modern-slide-menu {
    .menu-header {
      padding: 16px;

      .logo-section {
        .logo-icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          i {
            font-size: 20px;
          }
        }

        .logo-text {
          h3 {
            font-size: 16px;
          }

          p {
            font-size: 11px;
          }
        }
      }
    }

    .menu-content {
      .modern-menu {
        ::v-deep .el-menu-item,
        ::v-deep .el-submenu__title {
          height: 44px;
          line-height: 44px;
          margin: 2px 12px;
          padding: 0 16px !important;

          i {
            font-size: 16px;
            margin-right: 10px;
          }
        }
      }
    }

    .menu-footer {
      padding: 16px;

      .user-info {
        .user-avatar {
          width: 36px;
          height: 36px;
          margin-right: 10px;

          i {
            font-size: 16px;
          }
        }

        .user-details {
          .user-name {
            font-size: 13px;
          }

          .user-role {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
