import {
  getRequest,
  postBodyRequest,
  putBodyRequest,
  deleteBodyRequest,
  download
} from '../../assets/js/request';

import { baseUrl } from '../../config/constants';
// 用于数据回显
import {
  addFields,
  translate,
  addStatus,
  sortData,
  sortCapsData
} from './translate';

// 根据活动id获取报名信息
export const getSignUpListFromPc = params => {
  return getRequest(`${baseUrl}/signUp/getSignUpList.do?from=pc`, params)
    .then(addStatus)
    .then(sortData);
};
// 获取所有队长名单
export const getCaptainListFromPc = () => {
  return getRequest(`${baseUrl}/user/getUser.do?from=pc`)
    .then(addFields)
    .then(sortCapsData);
};
// 获取部门树
export const getDeptTreeFromPc = () => {
  return getRequest(`${baseUrl}/user/getDeptTree.do?from=pc`);
};
// 获取全部部门列表
export const getDeptListFromPc = () => {
  return getRequest(`${baseUrl}/user/getDept.do?from=pc`);
};
export const getSignUpFieldFromPc = params => {
  return getRequest(
    `${baseUrl}/field/public/get_sign_up_field.do?from=pc`,
    params
  ).then(translate);
};
export const deleteSignUpInfoFromPc = params => {
  return deleteBodyRequest(
    `${baseUrl}/signUp/public/deleted_sign_up.do?from=pc`,
    params
  );
};
// 管理员修改报名人信息
export const adminUpdateSignUpFromPc = params => {
  return postBodyRequest(
    `${baseUrl}/signUp/adminUpdateSignUp.do?from=pc`,
    params
  );
};
// 管理员审核报名
export const auditSignUpFromPc = params => {
  return postBodyRequest(
    `${baseUrl}/signUp/auditSignUp.do?actId=${params.actId}&ischeck=${params.ischeck}&from=pc`,
    params.signUpIdList
  );
};

export const getCaptainListFromMobile = () => {
  return getRequest(`${baseUrl}/user/getUser.do?from=mobile`);
};
export const signUpFromMobile = params => {
  return postBodyRequest(
    `${baseUrl}/signUp/public/add_sign_up.do?from=mobile`,
    params
  );
};
// export const getSignUpFieldFromMobile = params => {
//   return getRequest(`${baseUrl}/field/public/get_sign_up_field.do?from=mobile`, params).then(translate);
// };
export const getSignUpFieldFromMobile = params => {
  return getRequest(`${baseUrl}/field/public/getNoHideField.do`, params).then(
    translate
  );
};
export const getMySignUpFromMobile = params => {
  return getRequest(
    `${baseUrl}/signUp/public/get_my_sign_up.do?from=mobile`,
    params
  );
};
export const updateSignUpInfoFromMobile = params => {
  return putBodyRequest(
    `${baseUrl}/signUp/public/update_sign_up.do?from=mobile`,
    params
  );
};
export const deleteSignUpInfoFromMobile = params => {
  return deleteBodyRequest(
    `${baseUrl}/signUp/public/deleted_sign_up.do?from=mobile`,
    params
  );
};

export const exportSignUpExcel = params => {
  return download(
    `${baseUrl}/signUp/checkedExportToExcel.do`,
    params
  );
};
