<template>
  <div class="user-dropdown">
    <el-dropdown trigger="click" @command="handleCommand" placement="bottom-end">
      <div class="user-info">
        <img class="user-avatar" src="@/assets/images/avator.gif" alt="user-avatar">
        <div class="user-details">
          <span class="user-name">管理员</span>
          <span class="user-role">Administrator</span>
        </div>
        <i class="el-icon-arrow-down dropdown-icon"></i>
      </div>
      <el-dropdown-menu slot="dropdown" class="modern-user-dropdown">
        <el-dropdown-item command="profile" class="dropdown-item">
          <i class="el-icon-user"></i>
          <span>个人资料</span>
        </el-dropdown-item>
        <el-dropdown-item command="sync" class="dropdown-item">
          <i class="el-icon-refresh"></i>
          <span>同步通讯录</span>
        </el-dropdown-item>
        <el-dropdown-item command="changePass" class="dropdown-item">
          <i class="el-icon-key"></i>
          <span>修改密码</span>
        </el-dropdown-item>
        <el-dropdown-item divided command="logout" class="dropdown-item logout">
          <i class="el-icon-switch-button"></i>
          <span>退出登录</span>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 修改密码弹窗 -->
    <el-dialog
      title="修改密码"
      :visible.sync="dialogTableVisible"
      width="400px"
      :before-close="handleClose"
      class="password-dialog"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
      :z-index="3000"
    >
      <el-form :model="pwdFormData" :rules="rules" ref="pwdForm" label-width="80px">
        <el-form-item label="新密码" prop="newPwd">
          <el-input
            v-model="pwdFormData.newPwd"
            type="password"
            placeholder="请输入新密码"
            show-password
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="changePwd">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { syncAddressList } from '../api/activity/index';
import { LogoutPc, ChangePwdPc } from '../api/Login/index';

export default {
  name: 'UserDropdown',
  data() {
    return {
      dialogTableVisible: false,
      pwdFormData: {
        newPwd: ''
      },
      rules: {
        newPwd: [{
          required: true,
          min: 6,
          max: 16,
          message: '长度在6到16个字符',
          trigger: 'blur'
        }]
      }
    };
  },
  methods: {
    handleCommand(command) {
      if (command === 'sync') {
        this.syncAddressList();
      } else if (command === 'logout') {
        this.logout();
      } else if (command === 'changePass') {
        console.log('点击修改密码，当前状态:', this.dialogTableVisible);
        this.dialogTableVisible = true;
        console.log('设置后状态:', this.dialogTableVisible);
        // 强制触发视图更新
        this.$nextTick(() => {
          console.log('nextTick后状态:', this.dialogTableVisible);
        });
      } else if (command === 'profile') {
        this.$message.info('个人资料功能开发中...');
      }
    },

    syncAddressList() {
      syncAddressList().then(res => {
        if (res.data.status === 0) {
          this.$message.success('通讯录同步成功');
        } else {
          this.$message.error('通讯录同步失败');
        }
      }).catch(() => {
        this.$message.error('通讯录同步失败');
      });
    },

    changePwd() {
      this.$refs.pwdForm.validate((valid) => {
        if (valid) {
          ChangePwdPc({ newPwd: this.pwdFormData.newPwd }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success('密码修改成功');
              this.dialogTableVisible = false;
              this.pwdFormData.newPwd = '';
            } else {
              this.$message.error('密码修改失败');
            }
          }).catch(() => {
            this.$message.error('密码修改失败，请稍后重试');
          });
        } else {
          this.$message.error('请检查输入格式');
        }
      });
    },

    logout() {
      this.$confirm('确定要退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 强制修复遮罩层问题
        this.$nextTick(() => {
          const modals = document.querySelectorAll('.v-modal, .el-overlay');
          modals.forEach(modal => {
            modal.style.zIndex = '1998';
          });
        });
        LogoutPc().then((res) => {
          if (res.data.status === 200) {
            this.$message.success('退出登录成功');
            window.sessionStorage.removeItem('userInfo');
            this.$router.push('/Login');
          } else {
            this.$message.error('退出登录失败');
          }
        }).catch(() => {
          this.$message.error('退出登录失败');
        });
      });
    },

    handleClose() {
      this.dialogTableVisible = false;
      this.pwdFormData.newPwd = '';
    }
  }
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.user-dropdown {
  .user-info {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      margin-right: 12px;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .user-details {
      display: flex;
      flex-direction: column;
      margin-right: 8px;

      .user-name {
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        line-height: 1.2;
      }

      .user-role {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        line-height: 1.2;
      }
    }

    .dropdown-icon {
      color: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      transition: transform 0.3s ease;
    }

    &:hover .dropdown-icon {
      transform: rotate(180deg);
    }
  }
}

// 修改密码弹窗样式
.password-dialog {
  .el-dialog {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #ebeef5;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }
}

// 全局弹窗样式覆盖
:global(.el-dialog.password-dialog) {
  background: #fff !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  z-index: 3000 !important;
}

:global(.el-dialog__wrapper) {
  z-index: 3000 !important;
}

:global(.el-overlay) {
  z-index: 1999 !important;
}

// 确保密码弹窗的遮罩层和弹窗本身都有足够高的层级
:global(.el-dialog__wrapper.password-dialog) {
  z-index: 3000 !important;
}

:global(.v-modal) {
  z-index: 1999 !important;
}

// 强制降低所有可能的遮罩层类名
:global(.el-popup-parent--hidden) {
  z-index: 1999 !important;
}

:global(.el-loading-mask) {
  z-index: 1999 !important;
}

// 针对 MessageBox 的遮罩层
:global(.el-message-box__wrapper .v-modal) {
  z-index: 1998 !important;
}

// 最强制的遮罩层修复 - 覆盖所有可能的遮罩层
:global([class*="modal"]:not(.el-dialog):not(.el-message-box)) {
  z-index: 1999 !important;
}

:global([class*="overlay"]:not(.el-dialog):not(.el-message-box)) {
  z-index: 1999 !important;
}

// 确保弹窗始终在最顶层
:global(.el-dialog) {
  z-index: 3000 !important;
  position: relative !important;
}

:global(.el-message-box) {
  z-index: 3002 !important;
  position: relative !important;
}

// 针对性修复密码弹窗被遮挡的问题
:global(.el-dialog__wrapper) {
  &:has(.password-dialog) {
    z-index: 3000 !important;
  }
}

// 确保所有相关的弹窗元素都有正确的层级
:global(.el-overlay-dialog) {
  z-index: 3000 !important;
}

</style>
