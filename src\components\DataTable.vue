<template>
  <div>
    <div class="options">
      <!-- 将父组件传过来的按钮数组实例化 -->
      <el-button
        v-for="(item, index) in buttonList"
        :key="index"
        :type="item.type"
        :size="item.size || 'mini'"
        :icon="item.icon"
        @click="handleFunc(selectedData, item.event, item.limit, item.only)"
      >
        {{ item.text }}
      </el-button>
      <!-- 搜索框，通过isShowSearch判断是否显示 -->
      <el-input
        v-if="isShowSearch"
        v-model="searchContent"
        size="mini"
        class="searchButton"
        :placeholder="searchPlaceholder"
        :clearable="true"
        @change="handleFunc(searchContent, 'search-content-changed', 0)"
      />
      <el-select
        v-if="isShowSelect"
        v-model="selectValue"
        :placeholder="selectPlaceholder"
        clearable
        size="mini"
        style="width: 250px; margin: 0 10px"
        @change="handleSelectChange"
      >
        <el-option
          v-for="item in selectOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <!-- tab筛选 -->
    <!-- <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="用户管理" name="first">用户管理</el-tab-pane>
    </el-tabs> -->
    <slot name="filter"></slot>

    <!-- 表格数据 -->
    <el-table
      ref="table-body"
      style="width: 100%"
      fit
      border
      stripe
      :data="tableData"
      :max-height="maxHeight"
      :row-class-name="'data-item'"
      row-key="id"
      @selection-change="handleSelectionChange"
      @row-click="handleClick"
      @row-dblclick="handleDblcilck"
      default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <!-- 是否在第一行显示多选按钮 -->
      <el-table-column v-if="isShowSelector" type="selection" width="55" />
      <!--
        表格数据展示
        默认是展示文本数据，如果要展示图片，需要将type字段置为'image'
        image的宽度会根据父级宽度进行自适应，如果要自定义则需另外传入宽高 { width: xxx, height: xxx }
      -->
      <!-- 添加pid用于解决发布活动时添加奖品信息  -->
      <el-table-column
        v-for="title in tableTitle"
        align="center"
        :key="title.pid || title.id"
        :prop="title.prop"
        :label="title.label"
        :fixed="title.fixed"
        :width="title.width ? title.width : ''"
        :sortable="title.sortable"
      >
        <template slot="header">
          <el-checkbox
            class="returnCheck"
            v-if="flag"
            @change="e => handleCheckboxChange(e, title.label)"
          />
          {{ title.label }}
        </template>
        <template slot-scope="scope">
          <el-image
            v-if="title.type === 'image'"
            :src="scope.row[title.prop].src"
            :style="`width: ${scope.row[title.prop].width}`"
          >
            <div slot="placeholder" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
          <template v-if="title.type === undefined">{{
            scope.row[title.prop]
          }}</template>
          <i
            v-if="title.type === 'icon'"
            :class="scope.row[title.prop]"
            @click="changeStatus(scope.row)"
          ></i>
          <div v-if="title.type === 'topping'">
            <el-dropdown
              trigger="click"
              @command="
                (args) => {
                  selectTopping(args, scope.row);
                }
              "
              class="topping"
            >
              <span class="el-dropdown-link topping">
                {{
                  scope.row[title.prop] !== 0
                    ? "权重" + scope.row[title.prop]
                    : "否"
                }}
              </span>
              <el-dropdown-menu slot="dropdown" :scope="scope.row">
                <el-dropdown-item :command="1">置顶权重1</el-dropdown-item>
                <el-dropdown-item :command="2">置顶权重2</el-dropdown-item>
                <el-dropdown-item :command="3">置顶权重3</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <i
              v-if="scope.row[title.prop] !== 0"
              title="取消置顶"
              class="el-icon-error cancel-topping"
              @click="cancelTopping(scope.row)"
            ></i>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页数据 -->
    <div class="block">
      <el-pagination
        background
        v-if="isShowPagination"
        layout="sizes, prev, pager, next, total, jumper"
        :current-page="pageIndex"
        :page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30]"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import { debounce } from "../utils/index";
export default {
  props: {
    // 默认显示表头的checkbox
    flag: {
      type: Boolean,
    },

    // 表单的首行
    tableTitle: {
      type: Array,
      default() {
        return [];
      },
    },

    // 表单数据，其中数据的键名需与对应title 的prop对应
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    selectOptions: {
      type: Array,
      default() {
        return [];
      },
    },
    // 表单的最高高度
    maxHeight: Number,

    // 是否在左边显示选择框
    isShowSelector: {
      type: Boolean,
      default: false,
    },

    // 是否显示搜索框
    isShowSearch: {
      type: Boolean,
      default: false,
    },

    // 是否显示分页工具
    isShowPagination: {
      type: Boolean,
      default: true,
    },

    // 是否显示选择框
    isShowSelect: {
      type: Boolean,
      default: false,
    },
    // 自定义操作按钮组
    buttonList: {
      type: Array,
      default() {
        return [];
      },
    },

    pageIndex: {
      type: Number,
      default: 0,
    },

    pageSize: {
      type: Number,
      default: 10,
    },

    total: {
      type: Number,
      default: 0,
    },

    searchPlaceholder: {
      type: String,
      default: "输入关键字搜索",
    },

    selectPlaceholder: {
      type: String,
      default: "请选择",
    },
  },
  data() {
    return {
      // 以选中的表单数据
      selectedData: [],
      that: this,
      // 绑定搜索内容
      searchContent: "",

      // 选定某列
      checkList: ["姓名"],
      selectValue: "",

      selectedTitle: [],
    };
  },
  methods: {
    debounce,

    /**
     * 用于获取触发事件的列表行数据，并且将数据分发给父组件，由父组件来进行相应的处理
     * @param {Array} row: 当前被点击的列表行数据
     * @param {String} event: 分发给父组件的事件名
     */
    handleFunc(data, event, limit, only) {
      if (only) {
        limit = limit || 0;
        if (data && data.length < limit && data.length <= only) {
          this.$message({
            message: `至少选择${limit}项任务再执行操作`,
            type: "error",
            duration: 1000,
          });
        } else if (data.length > only) {
          this.$message({
            message: `只能选择${only}条信息进行操作`,
            type: "error",
            duration: 1000,
          });
        } else {
          this.$emit(event, data);
        }
      } else {
        if (data && data.length < limit) {
          this.$message({
            message: `至少选择${limit}项任务再执行操作`,
            type: "error",
            duration: 1000,
          });
        } else {
          this.$emit(event, data);
        }
      }
    },

    handleCheckboxChange(val, title) {
      console.log(val, title)
      if (val) {
        this.selectedTitle.push(title);
      } else {
        this.selectedTitle = this.selectedTitle.filter(item => item !== val);
      }
      this.$emit('selectedTitleChange', this.selectedTitle)
    },

    // 已选中的表单内容，并将内容地址赋值给selectedData，用于传数据给父组件
    handleSelectionChange(val) {
      this.selectedData = val;
      console.log(val);
    },

    // 页码跳转处理函数
    handlePageChange(val) {
      this.debouncePageChange(val);
    },

    handleSizeChange(val) {
      this.$emit("page-size-change", val);
    },

    // 表格项双击处理 handleDblcilck(row, column, event)
    handleDblcilck(row) {
      this.handleFunc(row, "dblclick", 0);
    },

    // 表格项单击处理，默认处理如果有选择多选配置，则将该行加入已选行列
    handleClick(row) {
      if (!this.isShowSelector) {
        return -1;
      }
      this.$refs["table-body"].toggleRowSelection(row);
    },

    changeStatus(args) {
      this.$emit("changeStatus", args);
    },
    selectTopping(args, value) {
      this.$emit("selectTopping", args, value);
    },
    cancelTopping(args) {
      this.$emit("cancelTopping", args);
    },
    handleSelectChange(val) {
      this.$emit("select-change", val);
    },
    initSearch() {
      this.searchContent = "";
    },
    initSelect() {
      this.selectValue = "";
    },
  },
  created() {
    this.debouncePageChange = this.debounce(
      // 进行防抖处理的函数
      (pageIndex) => {
        this.$emit("page-index-change", pageIndex);
      },
      // 间隔时间
      500,
      // 立刻执行
      true,
      // 操作太快时执行的回调函数
      () => {
        // 当用户操作太快时进行弹窗提醒
        this.$message({
          message: "请不要频繁操作！",
          type: "warning",
          duration: 1000,
        });
        this.currentIndex = 1;
        this.$nextTick(() => {
          this.currentIndex = this.pageIndex;
        });
      }
    );
  },
};
</script>

<style lang="scss" scoped>
.options {
  display: flex;
  align-items: center;
  overflow: auto;
  margin-bottom: 20px;
}

.searchButton {
  width: 20vw;
  margin-left: 1vw;
}

deep.data-item {
  font-size: 13px !important;
}

deep.el-pagination {
  margin-top: 10px;
}
.el-icon-success {
  // color:$color-primary;
  color: green;
  cursor: pointer;
}
.el-icon-error {
  color: red;
  cursor: pointer;
}

deep.el-image {
  display: inline-block;
  > img {
    width: 100%;
  }
}
// 颜色集合
$color: (
  1: #ccc,
  2: pink,
  3: yellow,
);

@each $key, $val in $color {
  .topping-color--#{$key} {
    color: $val;
  }
}
.topping {
  cursor: pointer;
}
.cancel-topping {
  padding-left: 8px;
}
</style>

