<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>登录跳转中</title>
</head>

<body>
<script>
  // 获取 URL 参数
  function getUrlParam(name) {
    const params = new URLSearchParams(window.location.search);
    return params.get(name);
  }

  // 获取原始 URL 参数
  const params = window.location.search;

  // 获取跳转页面 URL
  let url = '';
  if (params.includes('--') && params.includes('&')) {
    url = params.slice(params.indexOf('--') + 2, params.indexOf('&'));
    const speStrIndex = url.indexOf('slicefrom=');
    if (speStrIndex !== -1) {
      const indexValue = parseInt(url.substring(speStrIndex + 10));
      url = url.substring(0, indexValue) + '#' + url.substring(indexValue, speStrIndex);
    }
  }

  const code = getUrlParam('code');

  fetch('/back/weixin/oauth2back.do?code=' + encodeURIComponent(code))
    .then(response => response.json())
    .then(res => {
      if (res.status === 2) {
        localStorage.setItem('user', res.data.name);
        window.location.replace(url);
      } else {
        alert('你暂无权限登陆此应用，可联系公司管理员');
        window.close();
      }
    })
    .catch(err => {
      alert('系统错误，请稍候再试');
      window.close();
    });
</script>
</body>
</html>
