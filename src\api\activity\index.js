import {
  getRequest,
  postBodyRequest,
  deleteRequest
} from '../../assets/js/request';

import { baseUrl } from '../../config/constants';
import { filterData } from './translator';

export const getActivityListFromPc = params => {
  return getRequest(
    `${baseUrl}/activity/public/query_activity_page.do?from=pc`,
    params
  );
};
export const addActivityFromPc = params => {
  return postBodyRequest(`${baseUrl}/activity/addOrUpdate.do?from=pc`, params);
};
export const getActivityDetailFromPc = params => {
  return getRequest(
    `${baseUrl}/activity/public/query_activity_detail.do?from=pc`,
    params
  );
};
export const publicActivityFromPc = params => {
  return postBodyRequest(`${baseUrl}/activity/addOrUpdate.do?from=pc`, params);
  // return postBodyRequest(`${baseUrl}/activity/add.do?from=pc`, params);
};
export const deleteActivityFromPc = params => {
  // return deleteRequest(`${baseUrl}/activity/delete_activity.do?actId=${params.actId}&actName=${params.actName}&code=${params.code}&from=pc`);
  return deleteRequest(
    `${baseUrl}/activity/delete_activity.do?actId=${params.actId}&code=${params.code}&from=pc`
  );
};
// 同步微信通讯录
export const syncAddressList = () => {
  return getRequest(`${baseUrl}/weixin/firstSynchronize.do`);
};

export const getActivityListFromMobile = params => {
  return getRequest(
    `${baseUrl}/activity/public/query_activity_page.do?from=mobile`,
    params
  ).then(filterData);
};
export const getActivityDetailFromMobile = params => {
  return getRequest(
    `${baseUrl}/activity/public/query_activity_detail.do?from=mobile`,
    params
  );
};
export const downFile = params => {
  return getRequest(`${baseUrl}/file/public/download`, params);
};
