// ===================================
// 美化全局颜色配置文件
// ===================================

// === 现代化配色方案 ===
// 主色调 - 优雅的深蓝色系
$color-primary: #2563eb;        // 现代蓝色 - 专业、可信赖
$color-primary-light: #3b82f6;  // 亮蓝色 - 悬停状态
$color-primary-lighter: #60a5fa; // 浅蓝色 - 背景色
$color-primary-dark: #1d4ed8;   // 深蓝色 - 激活状态
$color-primary-darker: #1e40af; // 更深蓝色 - 强调

// 次色调 - 温暖的紫色系
$color-secondary: #7c3aed;      // 优雅紫色 - 创意、高端
$color-secondary-light: #8b5cf6; // 亮紫色
$color-secondary-lighter: #a78bfa; // 浅紫色
$color-secondary-dark: #6d28d9;  // 深紫色
$color-secondary-darker: #5b21b6; // 更深紫色

// 辅助色调 - 清新的青色系
$color-tertiary: #06b6d4;       // 现代青色 - 清新、活力
$color-tertiary-light: #22d3ee; // 亮青色
$color-tertiary-lighter: #67e8f9; // 浅青色
$color-tertiary-dark: #0891b2;  // 深青色
$color-tertiary-darker: #0e7490; // 更深青色

// === 美化功能色 ===
// 成功色 - 自然绿色系
$color-success: #10b981;        // 现代绿色 - 成功、完成
$color-success-light: #34d399;  // 亮绿色
$color-success-dark: #059669;   // 深绿色

// 警告色 - 温暖橙色系
$color-warning: #f59e0b;        // 现代橙色 - 警告、注意
$color-warning-light: #fbbf24;  // 亮橙色
$color-warning-dark: #d97706;   // 深橙色

// 错误色 - 优雅红色系
$color-error: #ef4444;          // 现代红色 - 错误、危险
$color-error-light: #f87171;    // 亮红色
$color-error-dark: #dc2626;     // 深红色

// 信息色 - 柔和蓝色系
$color-info: #3b82f6;           // 信息蓝色 - 提示、信息
$color-info-light: #60a5fa;     // 亮信息蓝
$color-info-dark: #2563eb;      // 深信息蓝

// === 透明度变体 ===
$color-primary-05: rgba($color-primary, 0.05);
$color-primary-10: rgba($color-primary, 0.1);
$color-primary-20: rgba($color-primary, 0.2);
$color-primary-30: rgba($color-primary, 0.3);
$color-primary-50: rgba($color-primary, 0.5);
$color-primary-80: rgba($color-primary, 0.8);

$color-secondary-05: rgba($color-secondary, 0.05);
$color-secondary-10: rgba($color-secondary, 0.1);
$color-secondary-20: rgba($color-secondary, 0.2);
$color-secondary-30: rgba($color-secondary, 0.3);
$color-secondary-50: rgba($color-secondary, 0.5);
$color-secondary-80: rgba($color-secondary, 0.8);

// === 现代渐变色 ===
// 主要渐变
$gradient-primary: linear-gradient(135deg, $color-primary 0%, $color-primary-dark 100%);
$gradient-secondary: linear-gradient(135deg, $color-secondary 0%, $color-secondary-dark 100%);
$gradient-tertiary: linear-gradient(135deg, $color-tertiary 0%, $color-tertiary-dark 100%);

// 彩虹渐变
$gradient-rainbow: linear-gradient(135deg, $color-primary 0%, $color-secondary 50%, $color-tertiary 100%);
$gradient-sunset: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);
$gradient-ocean: linear-gradient(135deg, $color-primary 0%, $color-tertiary 100%);

// 柔和渐变
$gradient-soft-primary: linear-gradient(135deg, $color-primary-lighter 0%, $color-primary-light 100%);
$gradient-soft-secondary: linear-gradient(135deg, $color-secondary-lighter 0%, $color-secondary-light 100%);

// === 现代中性色系 ===
// 文字颜色 - 更好的可读性
$color-text-primary: #1f2937;     // 深灰色 - 主要文字
$color-text-secondary: #4b5563;   // 中灰色 - 次要文字
$color-text-tertiary: #6b7280;    // 浅灰色 - 辅助文字
$color-text-placeholder: #9ca3af; // 占位符灰色
$color-text-disabled: #d1d5db;    // 禁用状态灰色
$color-text-inverse: #ffffff;     // 反色文字

// 边框颜色 - 层次分明
$color-border-light: #f3f4f6;     // 浅边框
$color-border-base: #e5e7eb;      // 基础边框
$color-border-medium: #d1d5db;    // 中等边框
$color-border-dark: #9ca3af;      // 深边框
$color-border-darker: #6b7280;    // 更深边框

// 背景颜色 - 层次丰富
$color-background-white: #ffffff;  // 纯白背景
$color-background-light: #f9fafb;  // 浅灰背景
$color-background-base: #f3f4f6;   // 基础背景
$color-background-medium: #e5e7eb; // 中等背景
$color-background-dark: #d1d5db;   // 深背景
$color-background-darker: #9ca3af; // 更深背景

// 阴影颜色
$color-shadow-light: rgba(0, 0, 0, 0.05);
$color-shadow-base: rgba(0, 0, 0, 0.1);
$color-shadow-medium: rgba(0, 0, 0, 0.15);
$color-shadow-dark: rgba(0, 0, 0, 0.25);
$color-shadow-darker: rgba(0, 0, 0, 0.35);

// === CSS 自定义属性 (现代主题系统) ===
:root {
  // 主色系
  --color-primary: #{$color-primary};
  --color-primary-light: #{$color-primary-light};
  --color-primary-dark: #{$color-primary-dark};

  // 次色系
  --color-secondary: #{$color-secondary};
  --color-secondary-light: #{$color-secondary-light};
  --color-secondary-dark: #{$color-secondary-dark};

  // 辅助色系
  --color-tertiary: #{$color-tertiary};
  --color-tertiary-light: #{$color-tertiary-light};
  --color-tertiary-dark: #{$color-tertiary-dark};

  // 功能色系
  --color-success: #{$color-success};
  --color-warning: #{$color-warning};
  --color-error: #{$color-error};
  --color-info: #{$color-info};

  // 渐变色系
  --gradient-primary: #{$gradient-primary};
  --gradient-secondary: #{$gradient-secondary};
  --gradient-rainbow: #{$gradient-rainbow};
  --gradient-ocean: #{$gradient-ocean};

  // 文字色系
  --color-text-primary: #{$color-text-primary};
  --color-text-secondary: #{$color-text-secondary};
  --color-text-tertiary: #{$color-text-tertiary};

  // 背景色系
  --color-background-white: #{$color-background-white};
  --color-background-light: #{$color-background-light};
  --color-background-base: #{$color-background-base};

  // 边框色系
  --color-border-light: #{$color-border-light};
  --color-border-base: #{$color-border-base};
  --color-border-dark: #{$color-border-dark};
}

// === 现代化工具类 ===
// 文字颜色工具类
.text-primary { color: $color-primary !important; }
.text-secondary { color: $color-secondary !important; }
.text-tertiary { color: $color-tertiary !important; }
.text-success { color: $color-success !important; }
.text-warning { color: $color-warning !important; }
.text-error { color: $color-error !important; }
.text-info { color: $color-info !important; }

.text-gray-900 { color: $color-text-primary !important; }
.text-gray-700 { color: $color-text-secondary !important; }
.text-gray-500 { color: $color-text-tertiary !important; }
.text-gray-400 { color: $color-text-placeholder !important; }
.text-white { color: $color-text-inverse !important; }

// 背景颜色工具类
.bg-primary { background-color: $color-primary !important; }
.bg-secondary { background-color: $color-secondary !important; }
.bg-tertiary { background-color: $color-tertiary !important; }
.bg-success { background-color: $color-success !important; }
.bg-warning { background-color: $color-warning !important; }
.bg-error { background-color: $color-error !important; }
.bg-info { background-color: $color-info !important; }

.bg-white { background-color: $color-background-white !important; }
.bg-gray-50 { background-color: $color-background-light !important; }
.bg-gray-100 { background-color: $color-background-base !important; }
.bg-gray-200 { background-color: $color-background-medium !important; }

// 渐变背景工具类
.bg-gradient-primary { background: $gradient-primary !important; }
.bg-gradient-secondary { background: $gradient-secondary !important; }
.bg-gradient-tertiary { background: $gradient-tertiary !important; }
.bg-gradient-rainbow { background: $gradient-rainbow !important; }
.bg-gradient-ocean { background: $gradient-ocean !important; }
.bg-gradient-sunset { background: $gradient-sunset !important; }

// 边框颜色工具类
.border-primary { border-color: $color-primary !important; }
.border-secondary { border-color: $color-secondary !important; }
.border-tertiary { border-color: $color-tertiary !important; }
.border-gray-200 { border-color: $color-border-light !important; }
.border-gray-300 { border-color: $color-border-base !important; }
.border-gray-400 { border-color: $color-border-medium !important; }

// === 现代化混合器 ===
@mixin modern-button($color: 'primary', $size: 'medium') {
  @if $color == 'primary' {
    background: $gradient-primary;
    border: 1px solid $color-primary;
    color: $color-text-inverse;
    box-shadow: 0 2px 4px $color-shadow-light;

    &:hover {
      background: $color-primary-light;
      box-shadow: 0 4px 8px $color-shadow-base;
      transform: translateY(-1px);
    }

    &:active {
      background: $color-primary-dark;
      transform: translateY(0);
    }
  } @else if $color == 'secondary' {
    background: $gradient-secondary;
    border: 1px solid $color-secondary;
    color: $color-text-inverse;
    box-shadow: 0 2px 4px $color-shadow-light;

    &:hover {
      background: $color-secondary-light;
      box-shadow: 0 4px 8px $color-shadow-base;
      transform: translateY(-1px);
    }

    &:active {
      background: $color-secondary-dark;
      transform: translateY(0);
    }
  }

  // 尺寸样式
  @if $size == 'small' {
    padding: 8px 16px;
    font-size: 12px;
    border-radius: 6px;
  } @else if $size == 'medium' {
    padding: 12px 24px;
    font-size: 14px;
    border-radius: 8px;
  } @else if $size == 'large' {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: 10px;
  }

  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 500;
  cursor: pointer;
  outline: none;

  &:focus {
    box-shadow: 0 0 0 3px rgba($color-primary, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
}

@mixin modern-card($elevation: 'medium') {
  background: $color-background-white;
  border-radius: 12px;
  border: 1px solid $color-border-light;

  @if $elevation == 'low' {
    box-shadow: 0 1px 3px $color-shadow-light;
  } @else if $elevation == 'medium' {
    box-shadow: 0 4px 6px $color-shadow-light, 0 1px 3px $color-shadow-base;
  } @else if $elevation == 'high' {
    box-shadow: 0 10px 15px $color-shadow-base, 0 4px 6px $color-shadow-medium;
  }

  transition: box-shadow 0.3s ease;

  &:hover {
    @if $elevation == 'low' {
      box-shadow: 0 4px 6px $color-shadow-light, 0 1px 3px $color-shadow-base;
    } @else if $elevation == 'medium' {
      box-shadow: 0 10px 15px $color-shadow-base, 0 4px 6px $color-shadow-medium;
    } @else if $elevation == 'high' {
      box-shadow: 0 20px 25px $color-shadow-medium, 0 10px 10px $color-shadow-dark;
    }
  }
}

@mixin modern-input() {
  background: $color-background-white;
  border: 2px solid $color-border-base;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: $color-text-primary;
  transition: all 0.3s ease;

  &::placeholder {
    color: $color-text-placeholder;
  }

  &:focus {
    border-color: $color-primary;
    box-shadow: 0 0 0 3px $color-primary-10;
    outline: none;
  }

  &:hover:not(:focus) {
    border-color: $color-border-medium;
  }

  &:disabled {
    background: $color-background-base;
    color: $color-text-disabled;
    cursor: not-allowed;
  }
}

// === 兼容性变量 (保持向后兼容) ===
$theme-primary: $color-primary;
$theme-secondary: $color-secondary;
$theme-tertiary: $color-tertiary;
$promary-color: $color-primary; // 保持原有拼写错误的变量名
